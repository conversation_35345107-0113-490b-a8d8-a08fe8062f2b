# 需求文档

## 介绍

天赐作业表单需要实现字段间的关联逻辑功能。当某些单选字段选择特定值时，相关的依赖字段才会显示并变为必填。本需求旨在为现有表单系统增加字段依赖关系的配置和渲染能力。

## 需求

### 需求 1 - 表单字段依赖属性配置

**用户故事：** 作为表单模板配置人员，我希望能够为表单字段配置依赖关系，以便实现字段的条件显示逻辑。

#### 验收标准

1. WHEN 配置人员编辑表单字段 THEN 系统 SHALL 为每个基础组件新增 dependent 属性用于指定依赖的字段itemId
2. WHEN 配置人员设置依赖字段 THEN 系统 SHALL 提供单选框形式选择当前表单页面中的单选元素，也可通过输入框直接输入itemId
3. WHEN 配置人员编辑表单字段 THEN 系统 SHALL 为每个基础组件新增 dependentValue 属性用于指定依赖字段的触发值
4. WHEN 配置人员设置依赖值 THEN 系统 SHALL 优先提供下拉框形式从依赖字段的候选值列表中选择，以确保数据准确性和降低错误率
5. WHEN 配置人员保存表单模板 THEN 系统 SHALL 将 dependent 和 dependentValue 属性保存到表单JSON配置中

### 需求 2 - 表单渲染时的依赖逻辑处理

**用户故事：** 作为表单填写用户，我希望表单能够根据我的选择动态显示相关字段，以便只填写必要的信息。

#### 验收标准

1. WHEN 系统渲染表单 THEN 系统 SHALL 在原有直接渲染基础上增加依赖过滤逻辑
2. IF 表单字段的 dependent 属性为空 THEN 系统 SHALL 直接渲染该字段
3. IF 表单字段的 dependent 属性不为空 THEN 系统 SHALL 默认隐藏该字段
4. WHEN 依赖字段的值与 dependentValue 一致 THEN 系统 SHALL 显示被依赖的字段
5. WHEN 被依赖字段显示后 THEN 系统 SHALL 根据字段本身的 isReq 属性决定是否必填

### 需求 3 - 依赖字段值变更处理

**用户故事：** 作为表单填写用户，我希望当我修改依赖字段的选择时，相关的依赖字段能够正确响应变化。

#### 验收标准

1. WHEN 用户修改依赖字段的选择值 THEN 系统 SHALL 实时检查所有依赖该字段的组件
2. IF 新选择的值不满足依赖条件 THEN 系统 SHALL 自动隐藏相关的依赖字段
3. WHEN 依赖字段被隐藏 THEN 系统 SHALL 清空该字段已填写的值且不上报该字段数据
4. WHEN 依赖字段重新显示 THEN 系统 SHALL 恢复该字段的初始状态

### 需求 4 - 表单提交时的依赖验证

**用户故事：** 作为系统管理员，我希望表单提交时能够验证依赖关系的完整性，以确保数据的准确性。

#### 验收标准

1. WHEN 用户提交表单 THEN 系统 SHALL 检查每个字段的依赖项是否存在
2. IF 字段配置的 dependent 对应的元素不存在 THEN 系统 SHALL 阻止提交并提示错误
3. IF 字段配置的 dependentValue 对应的选项值不存在 THEN 系统 SHALL 阻止提交并提示错误
4. WHEN 所有依赖关系验证通过 THEN 系统 SHALL 允许表单正常提交
5. WHEN 提交验证失败 THEN 系统 SHALL 显示具体的依赖关系错误信息
