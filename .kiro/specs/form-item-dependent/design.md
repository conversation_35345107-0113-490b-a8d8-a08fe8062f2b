# 设计文档

## 概述

本设计文档描述了如何在现有的作业票动态表单系统中实现字段依赖关系功能。基于对系统架构的完整理解，该功能将在现有的六层架构中进行扩展，确保与现有系统的完美集成。

## 架构

### 现有系统架构分析（基于源码分析文档）

```
1. 配置管理层 (Configuration Layer)
   - jsTemplateUser/content.tsx (模板列表管理)
   - editorFrom.tsx (可视化编辑器)
   - config/defaultFormConfig.tsx (组件注册配置)
   - config/disposeRegistry.ts (组件属性配置)
   - components/formItem/index.tsx (配置层渲染)

2. 渲染引擎层 (Rendering Engine Layer)
   - components/preview/renderItem.tsx (动态渲染核心)
   - components/preview/renderTable.tsx (表格渲染)

3. 数据转换层 (Data Conversion Layer)
   - utils/formConverter.ts (数据转换主函数)
   - createTicketPage.tsx (页面级数据转换)

4. 可视化编辑器层 (Visual Editor Layer)
   - editorFrom.tsx (拖拽式表单设计器)
   - formConfig.tsx (表单配置页面)

5. 业务集成层 (Business Integration Layer)
   - createTicketPage.tsx (页面级数据转换与集成)
   - content/createTicket/* (各业务组件)

6. 组件库层 (Component Library Layer)
   - components/dispose/* (配置组件)
   - components/eventCover/* (事件覆盖组件)
   - components/formItem/lib/* (表单组件库)
```

### 扩展后的架构

```
在现有六层架构中扩展依赖关系功能：

1. 配置管理层 + 依赖配置
   - config/disposeRegistry.ts (新增dependent和dependentValue配置项)
   - config/defaultFormConfig.tsx (为组件添加默认依赖属性)

2. 渲染引擎层 + 依赖逻辑
   - components/preview/renderItem.tsx (添加依赖显示/隐藏逻辑)
   - utils/formDependencyManager.ts (新增表单依赖关系管理器)

3. 数据转换层 + 依赖验证
   - createTicketPage.tsx (添加依赖关系验证和数据转换处理)

4. 可视化编辑器层 + 依赖配置UI
   - components/dispose/index.tsx (添加依赖配置界面)
   - editorFrom.tsx (保存时验证依赖关系)

5. 业务集成层 + 依赖业务逻辑
   - createTicketPage.tsx (集成依赖关系到业务流程)

6. 组件库层 + 依赖组件
   - components/formItem/index.tsx (配置层预览依赖效果)
```

## 组件和接口

### 1. 配置管理层扩展

#### 文件位置: `src/pages/ticket/config/disposeRegistry.ts` (修改现有文件)

```typescript
// 为所有组件类型添加依赖配置项
const baseDependencyConfig: IDisposeConfig[] = [
  {
    label: "依赖字段",
    type: "select",
    name: "dependent",
    placeholder: "选择依赖的字段",
    options: [], // 动态获取当前表单中的单选字段
  },
  {
    label: "依赖值",
    type: "select",
    name: "dependentValue",
    placeholder: "选择依赖的触发值",
    options: [], // 动态获取依赖字段的候选值列表
  },
];

// 在每个组件的配置中添加依赖配置
export const input: IDisposeConfig[] = [
  ...existingInputConfig,
  ...baseDependencyConfig,
];

export const radio: IDisposeConfig[] = [
  ...existingRadioConfig,
  ...baseDependencyConfig,
];

// 其他组件类似处理...
```

#### 文件位置: `src/pages/ticket/config/defaultFormConfig.tsx` (修改现有文件)

```typescript
// 在defaultFormData中添加依赖属性
export const defaultFormData = {
  // 现有属性...
  formName: "",
  isReq: "",
  isPrint: true,

  // 新增依赖属性
  dependent: "", // 依赖字段的itemId
  dependentValue: 0, // 依赖字段的触发值

  // 其他现有属性...
};

// 所有组件自动继承defaultFormData中的依赖属性
export const component: ItemType[] = [
  {
    compName: "输入框",
    compType: "input",
    business: "",
    group: "base",
    formData: { ...defaultFormData }, // 自动包含dependent和dependentValue
  },
  {
    compName: "单选框",
    compType: "radio",
    business: "",
    group: "base",
    formData: { ...defaultFormData }, // 自动包含dependent和dependentValue
  },
  // 其他组件类似处理，都使用...defaultFormData即可
];
```

### 2. 依赖关系管理器

#### 文件位置: `src/pages/ticket/utils/formDependencyManager.ts` (新建)

```typescript
import { ItemType } from "types";

export class DependencyManager {
  private formFields: ItemType[];
  private fieldValues: Map<string, any> = new Map();
  private dependencyMap: Map<string, string[]> = new Map();

  constructor(formFields: ItemType[]) {
    this.formFields = this.flattenFormFields(formFields);
    this.buildDependencyMap();
  }

  // 扁平化表单字段（处理嵌套结构）
  private flattenFormFields(fields: ItemType[]): ItemType[] {
    const result: ItemType[] = [];

    const flatten = (items: ItemType[]) => {
      items.forEach((item) => {
        result.push(item);
        if (item.children && item.children.length > 0) {
          flatten(item.children);
        }
      });
    };

    flatten(fields);
    return result;
  }

  // 构建依赖关系映射
  buildDependencyMap(): void {
    this.formFields.forEach((field) => {
      const { dependent } = field.formData;
      if (dependent) {
        if (!this.dependencyMap.has(dependent)) {
          this.dependencyMap.set(dependent, []);
        }
        this.dependencyMap.get(dependent)!.push(field.itemId);
      }
    });
  }

  // 检查字段是否应该显示
  shouldShowField(fieldId: string): boolean {
    const field = this.formFields.find((f) => f.itemId === fieldId);
    if (!field || !field.formData.dependent) {
      return true; // 无依赖的字段默认显示
    }

    const dependentValue = this.fieldValues.get(field.formData.dependent);
    return dependentValue === field.formData.dependentValue;
  }

  // 更新字段值并返回受影响的字段
  updateFieldValue(fieldId: string, value: any): string[] {
    this.fieldValues.set(fieldId, value);
    return this.dependencyMap.get(fieldId) || [];
  }

  // 获取所有隐藏的字段ID
  getHiddenFields(): string[] {
    return this.formFields
      .filter((field) => !this.shouldShowField(field.itemId))
      .map((field) => field.itemId);
  }

  // 验证依赖关系配置
  validateDependencyConfig(): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    const fieldIds = new Set(this.formFields.map((f) => f.itemId));

    this.formFields.forEach((field) => {
      const { dependent, dependentValue } = field.formData;

      // 检查依赖字段是否存在
      if (dependent && !fieldIds.has(dependent)) {
        errors.push(
          `字段 ${field.formData.formName || field.itemId} 依赖的字段 ${dependent} 不存在`
        );
      }

      // 检查依赖值是否有效
      if (dependent) {
        const dependentField = this.formFields.find(
          (f) => f.itemId === dependent
        );
        if (dependentField && dependentField.compType === "radio") {
          const validValues =
            dependentField.formData.candidateList?.map((c) => c.id) || [];
          if (dependentValue !== 0 && !validValues.includes(dependentValue)) {
            errors.push(
              `字段 ${field.formData.formName || field.itemId} 的依赖值 ${dependentValue} 在依赖字段的选项中不存在`
            );
          }
        }
      }
    });

    return { valid: errors.length === 0, errors };
  }
}
```

### 3. 渲染引擎层扩展

#### 文件位置: `src/pages/ticket/components/preview/renderItem.tsx` (修改现有文件)

```typescript
import { DependencyManager } from '../../utils/formDependencyManager';

// 添加依赖管理的 Hook
const useDependencyManager = (allFormFields: ItemType[]) => {
  const [dependencyManager] = useState(() => new DependencyManager(allFormFields));
  const [visibleFields, setVisibleFields] = useState<Set<string>>(new Set());
  const formApi = useFormApi();

  useEffect(() => {
    // 初始化可见字段
    const initialVisible = new Set<string>();
    const flatFields = dependencyManager['formFields']; // 访问私有属性

    flatFields.forEach(field => {
      if (dependencyManager.shouldShowField(field.itemId)) {
        initialVisible.add(field.itemId);
      }
    });
    setVisibleFields(initialVisible);
  }, [allFormFields, dependencyManager]);

  const updateFieldVisibility = (fieldId: string, value: any) => {
    const affectedFields = dependencyManager.updateFieldValue(fieldId, value);
    const newVisibleFields = new Set(visibleFields);

    affectedFields.forEach(affectedFieldId => {
      if (dependencyManager.shouldShowField(affectedFieldId)) {
        newVisibleFields.add(affectedFieldId);
      } else {
        newVisibleFields.delete(affectedFieldId);
        // 清空隐藏字段的值
        const fieldName = `form.${affectedFieldId}`;
        formApi.setValue(fieldName, undefined);
      }
    });

    setVisibleFields(newVisibleFields);
  };

  return { visibleFields, updateFieldVisibility, dependencyManager };
};

// 修改现有的 RenderItem 组件
export const RenderItem: FC<RenderItemProps & {
  allFormFields?: ItemType[];
  onFieldChange?: (fieldId: string, value: any) => void;
}> = ({
  item,
  k,
  isTable = false,
  isHighWork = false,
  rule = [],
  allFormFields = [],
  onFieldChange
}) => {
  const formApi = useFormApi();
  const { visibleFields, updateFieldVisibility } = useDependencyManager(allFormFields);

  // 如果字段不可见，直接返回 null
  if (allFormFields.length > 0 && !visibleFields.has(item.itemId)) {
    return null;
  }

  // 字段值变更处理函数
  const handleFieldChange = (value: any) => {
    if (allFormFields.length > 0) {
      updateFieldVisibility(item.itemId, value);
    }
    onFieldChange?.(item.itemId, value);
  };

  // 现有的字段渲染逻辑，在每个 case 中添加 onChange 处理
  switch (item?.compType ?? "") {
    case "radio":
      return (
        <Form.RadioGroup
          key={k}
          field={field}
          field-key={field}
          label={name}
          noLabel={isTable}
          onChange={handleFieldChange} // 添加依赖处理
          rules={[
            {
              required: Boolean(item?.formData?.isReq === "required"),
              message: `${name}为必填项`,
            },
          ]}
        >
          {(item?.formData?.candidateList ?? []).map(
            (o: any, index: number) => (
              <Form.Radio value={o?.id ?? index} key={index}>
                {o.label}
              </Form.Radio>
            )
          )}
        </Form.RadioGroup>
      );

    case "input":
      return isInputNumber ? (
        <Form.InputNumber
          key={`${k}-${formApi.getValue("form.level")}-${formApi.getValue("form.isUpgrade")}`}
          field={field}
          field-key={field}
          className="w-full"
          initValue={item?.formData?.defaultText}
          label={name}
          noLabel={isTable}
          precision={item?.formData?.type == "int" ? 0 : 2}
          placeholder={item?.formData?.placeHolder}
          helpText={renderHelpText()}
          validate={asyncValidate}
          onChange={handleFieldChange} // 添加依赖处理
          rules={[
            {
              required: Boolean(item?.formData?.isReq === "required"),
              message: `${name}为必填项`,
            },
          ]}
        />
      ) : (
        <Form.Input
          key={k}
          field={field}
          field-key={field}
          className="w-full"
          initValue={item?.formData?.defaultText}
          label={name}
          noLabel={isTable}
          placeholder={item?.formData?.placeHolder}
          onChange={handleFieldChange} // 添加依赖处理
          rules={[
            {
              required: Boolean(item?.formData?.isReq === "required"),
              message: `${name}为必填项`,
            },
          ]}
        />
      );

    // 其他 case 类似处理，都添加 onChange={handleFieldChange}
    // ... 保持现有逻辑不变，只添加依赖处理
  }
};
```

### 4. 可视化编辑器层扩展

#### 文件位置: `src/pages/ticket/components/dispose/index.tsx` (修改现有文件)

```typescript
// 在 Dispose 组件中添加依赖字段的动态选项获取
export const Dispose = ({ visible, data, onClose, onChangeCb }) => {
  const { containerState } = useContext(FormContext);

  // 获取当前表单中的单选字段作为依赖选项
  const getRadioFieldOptions = useCallback(() => {
    const flatFields = flattenFormFields(containerState.value);
    return flatFields
      .filter(field => field.compType === 'radio')
      .map(field => ({
        value: field.itemId,
        label: field.formData?.formName || field.compName,
      }));
  }, [containerState.value]);

  // 获取选中依赖字段的候选值选项
  const getDependentValueOptions = useCallback((dependentFieldId: string) => {
    if (!dependentFieldId) return [];

    const flatFields = flattenFormFields(containerState.value);
    const dependentField = flatFields.find(field => field.itemId === dependentFieldId);

    if (dependentField && dependentField.formData.candidateList) {
      return dependentField.formData.candidateList.map(item => ({
        value: item.id,
        label: item.label,
      }));
    }
    return [];
  }, [containerState.value]);

  // 在渲染配置项时，为 dependent 和 dependentValue 字段提供动态选项
  const renderConfigItem = (config: IDisposeConfig) => {
    if (config.name === 'dependent') {
      return (
        <Form.Select
          field={config.name}
          label={config.label}
          placeholder={config.placeholder}
        >
          <Form.Select.Option value="">无依赖</Form.Select.Option>
          {getRadioFieldOptions().map(option => (
            <Form.Select.Option key={option.value} value={option.value}>
              {option.label}
            </Form.Select.Option>
          ))}
        </Form.Select>
      );
    }

    if (config.name === 'dependentValue') {
      const currentDependentField = formApi.getValue('dependent');
      const valueOptions = getDependentValueOptions(currentDependentField);

      return (
        <Form.Select
          field={config.name}
          label={config.label}
          placeholder={config.placeholder}
          disabled={!currentDependentField}
        >
          <Form.Select.Option value={0}>请先选择依赖字段</Form.Select.Option>
          {valueOptions.map(option => (
            <Form.Select.Option key={option.value} value={option.value}>
              {option.label}
            </Form.Select.Option>
          ))}
        </Form.Select>
      );
    }

    // 其他配置项保持原有逻辑
    return renderOriginalConfigItem(config);
  };

  // ... 其余组件逻辑保持不变
};
```

#### 文件位置: `src/pages/ticket/editorFrom.tsx` (修改现有文件)

```typescript
import { DependencyManager } from "./utils/formDependencyManager";

// 在 saveFormData 函数中添加依赖关系验证
const saveFormData = () => {
  const filteredData = filterData(containerState.value);

  // 新增：验证依赖关系配置
  try {
    const dependencyManager = new DependencyManager(filteredData);
    const validation = dependencyManager.validateDependencyConfig();
    if (!validation.valid) {
      Toast.error(`依赖关系配置错误: ${validation.errors.join(", ")}`);
      return;
    }
  } catch (error) {
    Toast.error("依赖关系验证失败");
    console.error("Dependency validation error:", error);
    return;
  }

  if (isModal && onSave) {
    setIsChange(false);
    onSave(filteredData);
    return;
  }

  // 原有的保存逻辑
  mutation.mutate({
    values: {
      formTemplate: JSON.stringify(filteredData),
    },
    id: params.id,
  });
};

// 在 filterData 函数中保留依赖属性
const filterData = (data) => {
  const fieldsToOmit = [
    "selected",
    "chosen",
    "value",
    "parentId",
    "group",
    "options",
    "serviceRange",
    // 注意：不要过滤 dependent 和 dependentValue
  ];

  return data.map((item) => {
    const filteredItem = {};
    for (const key in item) {
      if (!fieldsToOmit.includes(key)) {
        if (key === "children") {
          filteredItem[key] = filterData(item[key]);
        } else {
          filteredItem[key] = item[key];
          if (item[key]?.formName === "表格" && key === "formData") {
            filteredItem[key] = omit(["formName"], item[key]);
          }
          if (item[key]?.formName === "文本" && key === "formData") {
            if (item[key]?.actualValue) {
              filteredItem[key] = omit(["formName"], item[key]);
            }
          }
        }
      }
    }
    return filteredItem;
  });
};
```

### 5. 数据转换层扩展

#### 文件位置: `src/pages/ticket/createTicketPage.tsx` (修改现有文件)

```typescript
import { DependencyManager } from "./utils/formDependencyManager";

// 在 convertForm 函数中处理依赖字段的数据转换
const convertForm = (
  form: any,
  elements: any[] = JSON.parse(tmpl.formTemplate)
): any[] => {
  const results: any[] = [];

  // 创建依赖管理器来获取隐藏字段
  const dependencyManager = new DependencyManager(elements);

  // 更新依赖管理器的字段值
  Object.keys(form).forEach((key) => {
    const item = elements.find(
      (el) => el.business === key || el.itemId === key
    );
    if (item) {
      dependencyManager.updateFieldValue(item.itemId, form[key]);
    }
  });

  // 获取隐藏的字段ID
  const hiddenFields = new Set(dependencyManager.getHiddenFields());

  Object.keys(form).forEach((key) => {
    let value = form[key];
    const business = find(propEq(key, "business"))(elements);
    const itemId = find(propEq(key, "itemId"))(elements);
    const item = business ? business : itemId;

    // 跳过隐藏字段的数据处理
    if (item && hiddenFields.has(item.itemId)) {
      return;
    }

    // 现有的数据处理逻辑保持不变
    if (item && item.compType === "employeePicker") {
      // 处理人员选择器数据
    } else if (item && item.compType === "annexImgPicker") {
      // 处理图片附件数据
    }
    // ... 其他类型处理
  });

  return results;
};
```

### 6. 配置层预览扩展

#### 文件位置: `src/pages/ticket/components/formItem/index.tsx` (修改现有文件)

```typescript
// 在配置层添加依赖关系的预览效果
export default function renderFormItem(
  current: any[],
  { renderChild, parent = null }
) {
  // 创建依赖管理器用于预览
  const dependencyManager = useMemo(() => {
    return new DependencyManager(current);
  }, [current]);

  return (
    current &&
    current.map((item, idx) => {
      // 检查字段是否应该显示（预览模式下的依赖逻辑）
      const shouldShow = dependencyManager.shouldShowField(item.itemId);

      // 现有的组件渲染逻辑
      let comp = null;
      const label = (
        <>
          <span className="text-sm font-medium">
            {item.formData?.formName || item.compName}:
          </span>
          {/* 添加依赖关系提示 */}
          {item.formData?.dependent && (
            <span className="text-xs text-gray-500 ml-2">
              (依赖: {item.formData.dependent} = {item.formData.dependentValue})
            </span>
          )}
        </>
      );

      // ... 现有的 switch 逻辑保持不变

      return (
        <div
          className={[
            "w-full bg-white rounded-md p-0 ",
            item.compType === "table" ? "col-span-3" : "",
            !shouldShow ? "opacity-50" : "", // 预览时显示依赖效果
          ].join(" ")}
          key={item.itemId}
          comp-type={item.compType}
        >
          {comp}
        </div>
      );
    })
  );
}
```

## 数据模型

### 表单配置数据结构扩展 (基于现有格式)

```json
{
  "compId": "5",
  "itemId": "formId-8R8E64yP",
  "business": "unitCategory",
  "children": [],
  "compName": "单选框",
  "compType": "radio",
  "formData": {
    "isReq": "required",
    "dependent": "", // 新增：依赖字段ID
    "dependentValue": 0, // 新增：依赖触发值
    "isPrint": true,
    "options": "",
    "formName": "作业单位类别",
    "candidateList": [
      { "id": 1, "label": "本厂" },
      { "id": 2, "label": "承包商" }
    ]
  },
  "nodeIndex": 5
}
```

## 实现注意事项

### 兼容性

- 现有表单配置不包含 `dependent` 和 `dependentValue` 时，字段正常显示
- 渐进式升级，不影响现有功能
- 保持与现有六层架构的完美集成

### 性能

- 只在有依赖关系的表单中启用依赖管理
- 使用 React.memo 优化字段组件渲染
- 依赖关系计算采用增量更新

### 用户体验

- 字段隐藏时清空已填写的值
- 在配置层提供清晰的依赖关系配置界面
- 在预览时显示依赖关系的效果

### 扩展性

- 基于现有的组件扩展机制
- 遵循现有的配置驱动设计理念
- 支持未来更复杂的依赖条件扩展

## 待讨论问题

### dependentValue 数据类型扩展

**当前设计限制：**

- 当前设计假设 `dependentValue` 为 `number` 类型
- 基于原始需求："给出候选的选项值，必须是个数字(单选目前的key都是数字)"
- 配置界面使用 `InputNumber` 组件限制输入

**潜在扩展需求：**
如果未来需要支持 `dependentValue` 为字符串类型，需要考虑以下问题：

1. **数据类型兼容性**

   - 需要支持混合类型的值比较（数字 vs 字符串）
   - 可能出现 `"1"` vs `1` 的类型不匹配问题
   - 需要考虑类型转换的一致性

2. **验证逻辑复杂性**

   - `candidateList` 中的 `id` 可能是字符串、数字或混合类型
   - 需要处理类型转换和比较的逻辑
   - 空值的默认处理（当前用 `0`，字符串可能用 `""` 或 `null`）

3. **配置界面改进**

   - 可以根据选择的依赖字段，动态显示该字段的候选值列表
   - 用户可以从下拉框中选择具体的值，而不是手动输入
   - 这样既支持数字也支持字符串，还能避免输入错误

4. **业务场景扩展**

   - 可以支持更多字段类型（如下拉选择器的字符串值）
   - 可以支持更复杂的业务逻辑（如状态码、分类代码等）
   - 更符合实际业务中的数据类型多样性

5. **向后兼容性**
   - 现有的数字类型配置如何迁移
   - 新旧版本的数据格式兼容
   - 默认值的处理策略

**建议的解决方案：**

- 使用联合类型：`dependentValue: string | number`
- 智能类型转换：在比较时进行适当的类型转换
- 动态配置界面：根据依赖字段的类型动态显示配置选项
- 向后兼容：保持对现有数字配置的支持

**决策状态：** 待后续讨论和确认具体实现方案
