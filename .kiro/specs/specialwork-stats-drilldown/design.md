# Design Document

## Overview

为特殊作业首页右侧排行榜组件（Right）中每一行的数字（`o?.totalNum`）增加下钻功能。当用户点击排行榜中任意一行的数字时，弹出浮层展示该项目的作业票分类饼图。该功能将复用现有的通用饼图组件 `PieChart`，并利用现有 API 接口 `postJobSliceRank` 返回的 `categoryInfo` 数据。

## Architecture

### 组件层次结构

```
Right (现有组件)
├── 排行榜列表
│   ├── 排行榜项目行
│   │   ├── 排名图标
│   │   ├── 项目名称
│   │   └── 数字 (totalNum) - 添加点击事件
└── SpecialWorkStatsModal (新增组件)
    └── PieChart (复用现有组件)
```

### 数据流

```
用户点击 totalNum
    ↓
触发 handleDrilldown 事件
    ↓
传递当前项目的 categoryInfo 数据
    ↓
显示 SpecialWorkStatsModal 浮层
    ↓
PieChart 组件渲染饼图
```

## Components and Interfaces

### 1. SpecialWorkStatsModal 组件

**位置**: `src/components/chart/SpecialWorkStatsModal.tsx`

新增的模态框组件，用于展示下钻饼图。

```typescript
interface SpecialWorkStatsModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  categoryInfo: SpecialWorkCategoryInfo | null;
}

interface SpecialWorkCategoryInfo {
  total: number;
  jobCategoryStats: SpecialWorkCategoryStats[];
}

interface SpecialWorkCategoryStats {
  id: number;
  name: string;
  count: number;
  icon: string;
}
```

### 2. Right 组件修改

在现有的 Right 组件中添加下钻功能：

```typescript
// 新增状态
const [specialWorkStatsModal, setSpecialWorkStatsModal] = useState<{
  isOpen: boolean;
  title: string;
  categoryInfo: SpecialWorkCategoryInfo | null;
}>({
  isOpen: false,
  title: "",
  categoryInfo: null,
});

// 新增事件处理函数
const handleDrilldown = (item: RankItem, area: string) => {
  const entityName =
    item.area?.name || item.department?.name || item.contractor?.name;
  const areaTypeMap = {
    areaStatList: "区域",
    departmentStatList: "部门",
    contractorStatList: "承包商",
  };

  setSpecialWorkStatsModal({
    isOpen: true,
    title: `${entityName} - ${areaTypeMap[area]}作业分类`,
    categoryInfo: item.categoryInfo,
  });
};
```

### 3. PieChart 配置

复用现有的 PieChart 组件，配置如下：

```typescript
const pieChartConfig = {
  title: "作业分类统计",
  queryKey: [], // 不需要请求，直接使用传入数据
  queryFn: () => Promise.resolve({ data: validatedData.jobCategoryStats }),
  optionBuilder: buildPieOption({
    nameField: "name",
    valueField: "count",
    radius: ["40%", "70%"],
    center: ["50%", "50%"],
    colorList: [
      "#60B7FF",
      "#36C361",
      "#FFB84D",
      "#FF6B6B",
      "#9C88FF",
      "#FF9F43",
      "#26D0CE",
      "#FD79A8",
    ],
    legend: {
      orient: "vertical",
      left: "right",
      top: "middle",
      itemWidth: 12,
      itemHeight: 12,
      textStyle: { fontSize: 12 },
    },
    tooltip: {
      trigger: "item",
      formatter: "{a} <br/>{b}: {c} ({d}%)",
    },
  }),
  centerContent: buildPieCenterContent({
    totalField: "count",
    label: "作业总计",
  }),
  height: 400,
};
```

## Data Models

### API 数据结构

基于现有的 `postJobSliceRank` API 接口，数据结构如下：

```typescript
interface RankApiResponse {
  code: number;
  message: string;
  data: {
    areaStatList: AreaRankItem[];
    departmentStatList: DepartmentRankItem[];
    contractorStatList: ContractorRankItem[];
  };
}

interface AreaRankItem {
  area: { id: number; name: string };
  totalNum: number;
  categoryInfo: SpecialWorkCategoryInfo;
}

interface DepartmentRankItem {
  department: { id: number; name: string };
  totalNum: number;
  categoryInfo: SpecialWorkCategoryInfo;
}

interface ContractorRankItem {
  contractor: { id: number; name: string };
  totalNum: number;
  categoryInfo: SpecialWorkCategoryInfo;
}

interface SpecialWorkCategoryInfo {
  total: number;
  jobCategoryStats: SpecialWorkCategoryStats[];
}

interface SpecialWorkCategoryStats {
  id: number;
  name: string;
  count: number;
  icon: string;
}
```

### 组件内部状态

```typescript
interface SpecialWorkDrilldownState {
  isOpen: boolean;
  title: string;
  categoryInfo: SpecialWorkCategoryInfo | null;
}
```

## Error Handling

### 数据验证工具

**位置**: `src/utils/drilldownValidation.ts`

提供完整的数据验证和错误处理工具：

```typescript
// 主要验证函数
export function validateCategoryInfo(
  categoryInfo: unknown
): SpecialWorkCategoryInfo | null;
export function isCategoryInfoEmpty(
  categoryInfo: SpecialWorkCategoryInfo | null
): boolean;
export function getCategoryInfoErrorMessage(categoryInfo: unknown): string;
export function getSafeCategoryInfo(categoryInfo: unknown): {
  data: SpecialWorkCategoryInfo | null;
  error: string | null;
  isEmpty: boolean;
};
```

### 错误处理 Hooks

**位置**: `src/hooks/useErrorHandler.ts`

```typescript
interface UseErrorHandlerReturn {
  errorState: ErrorState;
  handleError: (
    error: string | Error,
    type?: "validation" | "network" | "unknown"
  ) => void;
  clearError: () => void;
  showErrorToast: (message: string, duration?: number) => void;
}
```

**位置**: `src/hooks/useLoadingState.ts`

```typescript
interface UseLoadingStateReturn {
  isLoading: boolean;
  startLoading: () => void;
  stopLoading: () => void;
  setLoading: (loading: boolean) => void;
}
```

### 错误边界组件

**位置**: `src/components/error/ErrorBoundary.tsx`

用于捕获组件级别的 JavaScript 错误：

```typescript
interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: React.ComponentType<{ error: Error; retry: () => void }>;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}
```

### 空状态组件

**位置**: `src/components/ui/EmptyState.tsx`

```typescript
// 通用空状态组件
export const EmptyState: React.FC<EmptyStateProps>;

// 特种作业专用空状态组件
export const SpecialWorkEmptyState: React.FC<{ onRetry?: () => void }>;
```

### 错误状态处理

在 SpecialWorkStatsModal 中的完整错误处理：

```typescript
// 数据验证和处理
const { data: validatedData, error: validationError, isEmpty } = getSafeCategoryInfo(categoryInfo)

// 错误状态显示
if (errorState.hasError) {
  return (
    <div className="flex items-center justify-center h-64 text-red-400">
      <div className="text-center">
        <div className="text-lg mb-2">
          {errorState.errorType === 'validation' ? '数据验证失败' : '数据加载失败'}
        </div>
        <div className="text-sm">{errorState.errorMessage}</div>
        <div className="mt-4 space-x-2">
          <button onClick={clearError}>重试</button>
          <button onClick={onClose}>关闭</button>
        </div>
      </div>
    </div>
  )
}

// 空数据状态显示
if (isEmpty) {
  return <SpecialWorkEmptyState onRetry={handleRetry} />
}

// 数据质量提示
{validatedData.jobCategoryStats.length < originalDataLength && (
  <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
    <div className="text-sm text-yellow-700">
      <strong>数据提示：</strong>
      部分数据格式不正确已被过滤，显示 {validatedData.jobCategoryStats.length} 项有效数据
    </div>
  </div>
)}
```

## Testing Strategy

### 单元测试

1. **SpecialWorkStatsModal 组件测试**

   - 测试模态框的显示和隐藏
   - 测试标题和数据的正确传递
   - 测试空数据状态的处理
   - 测试错误状态的处理
   - 测试加载状态的处理

2. **Right 组件修改测试**

   - 测试点击事件的触发
   - 测试下钻数据的正确提取
   - 测试不同分类模式下的标题生成

3. **数据验证函数测试** (`src/utils/__tests__/drilldownValidation.test.ts`)
   - 测试有效数据的验证
   - 测试无效数据的处理和过滤
   - 测试边界情况和异常处理
   - 测试错误信息生成
   - 测试空数据检查

### 集成测试

1. **完整下钻流程测试**

   - 从点击数字到显示饼图的完整流程
   - 测试不同分类模式下的下钻功能
   - 测试模态框的关闭功能

2. **PieChart 集成测试**
   - 测试 PieChart 组件与传入数据的兼容性
   - 测试饼图的正确渲染
   - 测试中心统计数据的计算

### 用户体验测试

1. **交互测试**

   - 测试点击响应速度
   - 测试模态框的动画效果
   - 测试键盘导航（ESC 键关闭）

2. **响应式测试**
   - 测试不同屏幕尺寸下的显示效果
   - 测试移动端的触摸交互

## File Structure

### 新增文件

```
src/
├── components/
│   ├── chart/
│   │   └── SpecialWorkStatsModal.tsx          # 主要模态框组件
│   ├── error/
│   │   └── ErrorBoundary.tsx                  # 错误边界组件
│   └── ui/
│       └── EmptyState.tsx                     # 空状态组件
├── hooks/
│   ├── useErrorHandler.ts                     # 错误处理 Hook
│   └── useLoadingState.ts                     # 加载状态 Hook
├── types/
│   └── drilldown.ts                          # 类型定义文件
└── utils/
    ├── drilldownValidation.ts                # 数据验证工具
    ├── drilldownValidation.md                # 验证工具文档
    └── __tests__/
        └── drilldownValidation.test.ts       # 验证工具测试
```

### 修改文件

```
src/pages/specialWork/content/specialWorkIndex/right.tsx  # 添加下钻功能
```

## Implementation Notes

### 关键技术决策

1. **数据来源**: 直接使用现有 API 返回的 `categoryInfo` 数据，无需额外请求
2. **组件复用**: 完全复用现有的 PieChart 组件，确保界面一致性
3. **状态管理**: 在 Right 组件内部管理下钻状态，避免全局状态污染
4. **类型安全**: 为所有数据结构定义明确的 TypeScript 接口
5. **错误处理**: 分层错误处理架构，包含数据验证、组件错误边界和用户反馈
6. **数据验证**: 自动过滤无效数据项，确保图表正常渲染
7. **用户体验**: 提供加载状态、错误恢复和数据质量提示

### 性能考虑

1. **数据缓存**: 利用现有的 React Query 缓存机制
2. **组件懒加载**: SpecialWorkStatsModal 可以考虑懒加载以减少初始包大小
3. **事件优化**: 使用 useCallback 优化事件处理函数
4. **错误处理优化**: 使用自定义 Hooks 管理错误和加载状态，避免重复渲染
5. **数据验证缓存**: 验证结果可以缓存，避免重复验证相同数据

### 可访问性

1. **键盘导航**: 支持 ESC 键关闭模态框
2. **焦点管理**: 模态框打开时正确管理焦点
3. **屏幕阅读器**: 为模态框添加适当的 ARIA 属性

### 浏览器兼容性

- 支持现代浏览器（Chrome 80+, Firefox 75+, Safari 13+）
- 使用标准的 CSS 和 JavaScript 特性
- 依赖现有的 Tailwind CSS 和 React 生态系统
