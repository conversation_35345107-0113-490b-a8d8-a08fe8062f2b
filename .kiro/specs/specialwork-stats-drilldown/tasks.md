# Implementation Plan

- [x] 1. 创建 SpecialWorkStatsModal 组件基础结构

  - 创建 `src/components/chart/SpecialWorkStatsModal.tsx` 文件
  - 定义 SpecialWorkStatsModal 组件的 TypeScript 接口
  - 实现基础的模态框结构和样式
  - 添加打开/关闭状态管理和动画效果
  - _Requirements: 1.3, 5.1, 5.2_

- [x] 2. 集成 PieChart 组件到 SpecialWorkStatsModal

  - 在 SpecialWorkStatsModal 中导入和使用 PieChart 组件
  - 配置 buildPieOption 工厂函数用于饼图渲染
  - 配置 buildPieCenterContent 工厂函数用于中心统计显示
  - 实现数据适配器将 categoryInfo 转换为 PieChart 所需格式
  - _Requirements: 3.1, 3.2, 3.3_

- [x] 3. 实现数据验证和错误处理逻辑

  - 创建 categoryInfo 数据验证函数
  - 实现空数据状态的 UI 显示
  - 添加数据格式错误的容错处理
  - 实现加载状态和错误状态的用户反馈
  - _Requirements: 1.4, 1.5_

- [x] 4. 修改 Right 组件添加下钻功能

  - 在 Right 组件中添加 drilldownModal 状态管理
  - 实现 handleDrilldown 事件处理函数
  - 为排行榜中的 totalNum 数字添加点击事件
  - 根据当前分类模式生成正确的浮层标题
  - _Requirements: 1.1, 2.1, 2.2, 2.3, 4.1, 4.2, 4.4_

- [x] 5. 实现模态框交互和用户体验功能

  - 添加点击外部区域关闭模态框功能
  - 实现 ESC 键关闭模态框功能
  - 添加模态框显示时禁止页面滚动
  - 实现模态框关闭时恢复页面滚动
  - 优化模态框的响应式设计和移动端适配
  - _Requirements: 1.3, 5.3, 5.4, 5.5_

- [x] 6. 添加 TypeScript 类型定义和接口

  - 创建 `src/types/drilldown.ts` 文件定义相关类型
  - 为 CategoryInfo 和 JobCategoryStats 添加类型定义
  - 为 SpecialWorkStatsModal 组件添加完整的 Props 类型
  - 为 Right 组件的新增状态添加类型定义
  - _Requirements: 4.3_

- [x] 7. 编写单元测试

  - 为 SpecialWorkStatsModal 组件编写测试用例
  - 为数据验证函数编写测试用例
  - 为 Right 组件的下钻功能编写测试用例
  - 测试不同数据状态下的组件行为
  - _Requirements: 1.4, 1.5, 2.4, 2.5_

- [ ] 8. 集成测试和用户体验优化
  - 测试完整的下钻流程从点击到显示
  - 验证不同分类模式下的功能正确性
  - 测试 PieChart 组件与传入数据的兼容性
  - 优化点击响应速度和动画效果
  - 进行跨浏览器兼容性测试
  - _Requirements: 3.4, 3.5, 4.5_
