# Requirements Document

## Introduction

为特殊作业首页右侧排行榜组件（Right）中每一行的数字（`o?.totalNum`）增加下钻功能。用户点击排行榜中任意一行的数字后，弹出浮层展示该区域/部门/承包商的作业票分类饼图，提供更详细的数据分析视图。该功能将复用现有的通用饼图组件 `PieChart`，确保代码的一致性和可维护性。

## Requirements

### Requirement 1

**User Story:** 作为系统用户，我希望点击右侧排行榜中每一行的数字时能看到该项目的详细作业分类分布，以便更好地了解特定区域/部门/承包商的作业情况

#### Acceptance Criteria

1. WHEN 用户点击排行榜中任意一行的 `totalNum` 数字 THEN 系统 SHALL 弹出浮层显示该项目的作业票分类饼图
2. WHEN 浮层显示时 THEN 系统 SHALL 使用与左侧饼图相同的数据源和样式风格
3. WHEN 用户点击浮层外部区域或关闭按钮 THEN 系统 SHALL 关闭浮层
4. WHEN 浮层中的饼图加载时 THEN 系统 SHALL 显示加载状态
5. WHEN 饼图数据为空时 THEN 系统 SHALL 显示"暂无数据"提示

### Requirement 2

**User Story:** 作为系统用户，我希望下钻浮层中的饼图能够根据当前选中的排行榜项目和筛选条件自动获取数据，以便查看特定项目的作业分类

#### Acceptance Criteria

1. WHEN 用户点击"按区域"模式下某一行的数字 THEN 系统 SHALL 在饼图中显示该区域的作业票分类数据
2. WHEN 用户点击"按部门"模式下某一行的数字 THEN 系统 SHALL 在饼图中显示该部门的作业票分类数据
3. WHEN 用户点击"按承包商"模式下某一行的数字 THEN 系统 SHALL 在饼图中显示该承包商的作业票分类数据
4. WHEN 请求下钻数据时 THEN 系统 SHALL 传递当前的时间范围参数（本月/本年/全部）
5. WHEN 请求下钻数据时 THEN 系统 SHALL 传递对应的实体ID（区域ID/部门ID/承包商ID）

### Requirement 3

**User Story:** 作为系统用户，我希望下钻浮层的饼图能够复用现有的通用组件，以便保持界面风格的一致性

#### Acceptance Criteria

1. WHEN 显示下钻饼图时 THEN 系统 SHALL 使用 `PieChart` 通用组件
2. WHEN 生成饼图配置时 THEN 系统 SHALL 使用 `buildPieOption` 工厂函数
3. WHEN 显示中心统计时 THEN 系统 SHALL 使用 `buildPieCenterContent` 工厂函数
4. WHEN 浮层样式渲染时 THEN 系统 SHALL 与现有卡片保持相同的设计风格
5. WHEN 饼图交互时 THEN 系统 SHALL 支持 tooltip 和 legend 等标准交互功能

### Requirement 4

**User Story:** 作为系统用户，我希望下钻功能能够正确识别和传递排行榜项目信息，以便获取准确的下钻数据

#### Acceptance Criteria

1. WHEN 用户点击排行榜项目时 THEN 系统 SHALL 正确识别当前选中的分类模式（按区域/按部门/按承包商）
2. WHEN 用户点击排行榜项目时 THEN 系统 SHALL 正确获取该项目的实体信息（area/department/contractor对象）
3. WHEN 请求下钻数据时 THEN 系统 SHALL 根据当前时间范围（type: 1/2/3）传递正确的筛选参数
4. WHEN 浮层标题显示时 THEN 系统 SHALL 根据点击的项目显示对应的标题文本（如"XX区域作业分类"）
5. WHEN 数据请求失败时 THEN 系统 SHALL 显示友好的错误提示信息

### Requirement 5

**User Story:** 作为系统用户，我希望下钻浮层具有良好的响应式设计和用户体验，以便在不同设备上都能正常使用

#### Acceptance Criteria

1. WHEN 浮层显示时 THEN 系统 SHALL 在页面中央显示模态框，背景添加遮罩层
2. WHEN 浮层内容加载时 THEN 系统 SHALL 自适应内容高度，最大高度不超过视窗的80%
3. WHEN 用户按下 ESC 键时 THEN 系统 SHALL 关闭浮层
4. WHEN 浮层显示时 THEN 系统 SHALL 禁止页面滚动
5. WHEN 浮层关闭时 THEN 系统 SHALL 恢复页面滚动功能
