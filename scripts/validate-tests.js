#!/usr/bin/env node

/**
 * 验证测试文件语法的脚本
 */

import fs from "fs";

console.log("🔍 验证测试文件语法...\n");

const testFiles = [
  "src/pages/ticket/components/__tests__/dependency-specific-issues.test.tsx",
  "src/pages/ticket/components/preview/__tests__/renderItem.visibility.test.tsx",
];

let hasErrors = false;

testFiles.forEach((filePath) => {
  console.log(`📄 检查文件: ${filePath}`);

  try {
    if (!fs.existsSync(filePath)) {
      console.error(`❌ 文件不存在: ${filePath}`);
      hasErrors = true;
      return;
    }

    const content = fs.readFileSync(filePath, "utf8");

    // 基本语法检查
    const checks = [
      {
        name: "导入语句",
        pattern: /import.*from.*['"]/,
        required: true,
      },
      {
        name: "describe 块",
        pattern: /describe\s*\(/,
        required: true,
      },
      {
        name: "it 测试用例",
        pattern: /it\s*\(/,
        required: true,
      },
      {
        name: "expect 断言",
        pattern: /expect\s*\(/,
        required: true,
      },
      {
        name: "vi.mock 调用",
        pattern: /vi\.mock\s*\(/,
        required: false,
      },
    ];

    checks.forEach((check) => {
      const found = check.pattern.test(content);
      if (check.required && !found) {
        console.error(`  ❌ 缺少 ${check.name}`);
        hasErrors = true;
      } else if (found) {
        console.log(`  ✅ 包含 ${check.name}`);
      }
    });

    console.log(`  📊 文件大小: ${(content.length / 1024).toFixed(2)} KB`);
    console.log(`  📝 行数: ${content.split("\n").length}`);
  } catch (error) {
    console.error(`❌ 读取文件失败: ${error.message}`);
    hasErrors = true;
  }

  console.log("");
});

if (hasErrors) {
  console.error("❌ 发现错误，请检查上述问题");
  process.exit(1);
} else {
  console.log("✅ 所有测试文件语法验证通过！");
  console.log("\n📋 下一步:");
  console.log("  1. 运行测试: node scripts/test-specific-issues.js");
  console.log(
    "  2. 或单独运行: npm test -- src/pages/ticket/components/__tests__/dependency-specific-issues.test.tsx"
  );
  console.log(
    "  3. 查看文档: src/pages/ticket/components/__tests__/SPECIFIC_ISSUES_TESTS.md"
  );
}
