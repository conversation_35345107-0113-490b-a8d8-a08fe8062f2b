import { Tag, Tooltip } from "@douyinfe/semi-ui";
import {
  JOB_REPORT_STATUS_MAP,
  PROJECTTHREESYNC_PROJECTTYPE_MAP,
  PROJECTTHREESYNC_STATUS_MAP,
} from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { CommonAtoms } from "types";
import { formatDate } from "utils";

export const basicInfoProjectThreeSyncFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const basicInfoProjectThreeSyncFnAtom = atom({
  refetch: () => {},
});

export const basicInfoProjectThreeSyncEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const basicInfoProjectThreeSyncDetailSideAtom = atomWithReset({
  id: "",
  show: false,
});

export const basicInfoProjectThreeSyncApplyModalAtom = atomWithReset({
  id: "",
  show: false,
  record: {},
});

export const basicInfoProjectThreeSyncDesignModalAtom = atomWithReset({
  id: "",
  show: false,
  record: {},
});

export const basicInfoProjectThreeSyncBuildModalAtom = atomWithReset({
  id: "",
  show: false,
  record: {},
});

export const basicInfoProjectThreeSyncTestProductionModalAtom = atomWithReset({
  id: "",
  show: false,
  record: {},
});

export const basicInfoProjectThreeSyncAcceptModalAtom = atomWithReset({
  id: "",
  show: false,
  record: {},
});

export const basicInfoProjectThreeSyncAttachmentModalAtom = atomWithReset({
  id: "",
  show: false,
  record: {},
});

export const basicInfoProjectThreeSyncConfigModalAtom = atom(false);

const basicInfoProjectThreeSyncShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 48,
  },
  // user-defined code here
  {
    title: "项目名称",
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "项目编号",
    dataIndex: "code",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "项目描述",
    dataIndex: "content",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "项目位置",
    dataIndex: "location",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "项目类型",
    dataIndex: "projectType",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(PROJECTTHREESYNC_PROJECTTYPE_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "状态",
    dataIndex: "status",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(PROJECTTHREESYNC_STATUS_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "上报状态",
    dataIndex: "reportStatus",
    isShow: false,
    ellipsis: true,
    width: 150,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(JOB_REPORT_STATUS_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
    renderText: (item, record, index) => {
      const i = find(propEq(item, "id"))(JOB_REPORT_STATUS_MAP);
      return i?.name ?? "-";
    },
  },
  {
    title: "上报结果",
    dataIndex: "reportResult",
    isShow: false,
    ellipsis: true,
    width: 150,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: "上报时间",
    dataIndex: "reportTime",
    isShow: false,
    ellipsis: true,
    width: 200,
    render: (item) => {
      const content = formatDate(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
    renderText: (text) => formatDate(text),
  },
];

const basicInfoProjectThreeSyncExtendColumns = [
  // user-defined code here
];

export const basicInfoProjectThreeSyncShowColumnsAtom = atom(
  basicInfoProjectThreeSyncShowColumns
);

export const basicInfoProjectThreeSyncColumnsAtom = atom([
  ...basicInfoProjectThreeSyncShowColumns,
  ...basicInfoProjectThreeSyncExtendColumns,
]);

/*export const basicInfoProjectThreeSyncColumnsAtom = atom(
  (get) => get(basicInfoProjectThreeSyncShowColumnsAtom).concat(get(basicInfoProjectThreeSyncExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(basicInfoProjectThreeSyncShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(basicInfoProjectThreeSyncExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const basicInfoProjectThreeSyncAtoms: CommonAtoms = {
  entity: "BasicInfoProjectThreeSync",
  filter: basicInfoProjectThreeSyncFilterAtom,
  Fn: basicInfoProjectThreeSyncFnAtom,
  editModal: basicInfoProjectThreeSyncEditModalAtom,
  configModal: basicInfoProjectThreeSyncConfigModalAtom,
  columns: basicInfoProjectThreeSyncColumnsAtom,
};
