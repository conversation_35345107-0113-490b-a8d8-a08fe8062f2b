import { Image, ImagePreview, Tag, Tooltip } from "@douyinfe/semi-ui";
import { JOB_REPORT_STATUS_MAP, VALID_NOTVALID_MAP } from "components";
import { base_url } from "config";
import dayjs from "dayjs";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { formatDate, millisecondsOfOnemonth } from "utils";

export const contractorEmployeeCertificateEditModal = atom({
  id: "",
  show: false,
});

export const contractorEmployeeCertificateConfigModalAtom = atom(false);

// 查询条件
export const contractorEmployeeCertificateFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

// 查询条件
export const contractorEmployeeCertificateFnAtom = atom({
  refetch: () => {},
});

export interface ContractorEmployeeCertificateRecord {
  id: number;
  contractor: {
    name: string;
    // Add other contractor fields as needed
  };
  contractorEmployee: {
    name: string;
    // Add other employee fields as needed
  };
  code: string;
  authorityTypeValue: string;
  images?: string;
  isValid?: boolean;
  reportStatus?: number;
  reportResult?: string;
  reportTime?: string;
  expireDate?: string;
  // Add other fields as needed based on the API response
}

export const contractorEmployeeCertificateColumnsAtom = atom([
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },

  {
    title: "承包商名称",
    dataIndex: "contractor",
    isShow: true,
    ellipsis: true,
    render: (item) => <p>{item?.name}</p>,
  },
  {
    title: "人员姓名",
    dataIndex: "contractorEmployee",
    isShow: true,
    ellipsis: true,
    render: (item) => <p>{item?.name}</p>,
  },
  {
    title: "证书类型",
    dataIndex: "certificateTypeValue",
    isShow: true,
    ellipsis: true,
    render: (r) => <p>{r?.dicValue ?? ""}</p>,
  },
  {
    title: "证书编码",
    dataIndex: "code",
    isShow: true,
    ellipsis: true,
  },
  /* {
    title: <Tooltip content="发证日期">发证日期</Tooltip>,
    dataIndex: 'issueDate',
    isShow: true,
    ellipsis: true,
    render: (text) => {
      return (
        <p>
          {text ? dayjs(text).format('YYYY-MM-DD') : null}
        </p>
      )
    }
  }, */
  {
    title: "到期日期",
    dataIndex: "expireDate",
    isShow: true,
    ellipsis: true,
    render: (text) => {
      if (!text) {
        return null;
      }
      const date = dayjs(text);
      const diff = date.diff(dayjs());
      const dateShow = date.format("YYYY-MM-DD");

      return (
        <Tooltip content={dateShow}>
          <p>
            {diff < millisecondsOfOnemonth ? (
              <Tag color="red">{dateShow}</Tag>
            ) : diff < 3 * millisecondsOfOnemonth ? (
              <Tag color="yellow">{dateShow}</Tag>
            ) : (
              <Tag color="white">{dateShow}</Tag>
            )}
          </p>
        </Tooltip>
      );
    },
  },
  {
    title: "发证机关",
    dataIndex: "authorityTypeValue",
    isShow: true,
    ellipsis: true,
    render: (r) => <p>{r?.dicValue ?? ""}</p>,
  },
  {
    title: "证书图片",
    dataIndex: "images",
    isShow: true,
    ellipsis: true,
    render: (record) => {
      if (!record) {
        return null;
      }
      let list;
      try {
        list = JSON.parse(record);
        if (list && !Array.isArray(list) && list.values) {
          // 处理脏数据
          list = list.values;
        }
        if (!Array.isArray(list)) {
          list = [];
        }
      } catch (e) {
        list = [];
      }

      return (
        <ImagePreview>
          {list.map((o) => (
            <Image width={30} height={30} src={`${base_url}${o}`} />
          ))}
        </ImagePreview>
      );
    },
  },
  {
    title: "是否有效",
    dataIndex: "isValid",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(VALID_NOTVALID_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "上报状态",
    dataIndex: "reportStatus",
    isShow: false,
    ellipsis: true,
    width: 150,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(JOB_REPORT_STATUS_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
    renderText: (item, record, index) => {
      const i = find(propEq(item, "id"))(JOB_REPORT_STATUS_MAP);
      return i?.name ?? "-";
    },
  },
  {
    title: "上报结果",
    dataIndex: "reportResult",
    isShow: false,
    ellipsis: true,
    width: 150,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: "上报时间",
    dataIndex: "reportTime",
    isShow: false,
    ellipsis: true,
    width: 200,
    render: (item) => {
      const content = formatDate(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
    renderText: (text) => formatDate(text),
  },
]);
