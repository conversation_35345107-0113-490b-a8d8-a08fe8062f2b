import { Tag, Tooltip } from "@douyinfe/semi-ui";
import {
  BIM_CONTRACTOR_ISBLACK_MAP,
  BIM_CONTRACTOR_TYPE_MAP,
  JOB_REPORT_STATUS_MAP,
} from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { CommonAtoms } from "types";
import { formatDate } from "utils";

export const contractorFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const contractorFnAtom = atom({
  refetch: () => {},
});

export const contractorDetailSideAtom = atomWithReset({
  id: "",
  show: false,
});

export const contractorEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const contractorConfigModalAtom = atom(false);

export const contractorColumnsAtom = atom([
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  {
    title: "承包商类型",
    dataIndex: "contractorType",
    isShow: true,
    width: 120,
    render: (item) => {
      const i = find(propEq(item ? item : 1, "id"))(BIM_CONTRACTOR_TYPE_MAP);
      return <p>{i?.name ?? "-"}</p>;
    },
  },
  {
    title: "承包商名称",
    dataIndex: "name",
    isShow: true,
    // TODO
    //sorter: (a, b) => (a.name.length - b.name.length > 0 ? 1 : -1),
  },
  {
    title: "联络人",
    dataIndex: "firstContractor",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "联络人电话",
    dataIndex: "firstMobile",
    isShow: true,
  },
  {
    title: "是否黑名单",
    dataIndex: "isBlack",
    isShow: true,
    render: (item) => {
      const i = find(propEq(item, "id"))(BIM_CONTRACTOR_ISBLACK_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color ?? "-"} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "拉黑原因",
    dataIndex: "blackReason",
    isShow: false,
    ellipsis: true,
  },
  {
    title: <Tooltip content="上报状态">上报状态</Tooltip>,
    dataIndex: "reportStatus",
    isShow: false,
    ellipsis: true,
    header: "上报状态",
    render: (item: number) => {
      const i = find(propEq(item, "id"))(JOB_REPORT_STATUS_MAP) as
        | { id: number; name: string; color: string }
        | undefined;
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color ?? "-"} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
    renderText: (item: number) => {
      const i = find(propEq(item, "id"))(JOB_REPORT_STATUS_MAP) as
        | { id: number; name: string; color: string }
        | undefined;
      return i?.name ?? "-";
    },
  },
  {
    title: <Tooltip content="上报时间">上报时间</Tooltip>,
    dataIndex: "reportTime",
    isShow: false,
    ellipsis: true,
    header: "上报时间",
    render: (item) => {
      const content = formatDate(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
    renderText: (item) => formatDate(item) ?? "-",
  },
  {
    title: <Tooltip content="上报结果">上报结果</Tooltip>,
    dataIndex: "reportResult",
    isShow: false,
    ellipsis: true,
    header: "上报结果",
    render: (item, record, index) => (
      <Tooltip content={item ?? ""}>
        <p>{item ?? ""}</p>
      </Tooltip>
    ),
    renderText: (item, record, index) => item ?? "-",
  },
  // TODO: 是否更新怎么做
  /* {
    title: "是否有更新",
  }, */
]);

export const contractorShareModalAtom = atomWithReset({
  contractor: {},
  show: false,
});

export const contractorBlackModalAtom = atomWithReset({
  contractor: {},
  show: false,
});

export const contractorAtoms: CommonAtoms = {
  entity: "Contractor",
  filter: contractorFilterAtom,
  Fn: contractorFnAtom,
  editModal: contractorEditModalAtom,
  configModal: contractorConfigModalAtom,
  columns: contractorColumnsAtom,
};
