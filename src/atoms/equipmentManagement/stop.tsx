import { EQUIPMENT_STOPTYPE_MAP, JOB_REPORT_STATUS_MAP } from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { Tag, Tooltip } from "tdesign-react";
import { CommonAtoms } from "types";
import { formatDate } from "utils";

export const equipmentManagementStopFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const equipmentManagementStopFnAtom = atom({
  refetch: () => {},
});

export const equipmentManagementStopEditModalAtom = atomWithReset({
  id: "",
  show: false,
  equipment: null,
});

export const equipmentManagementStopConfigModalAtom = atom(false);

const equipmentManagementStopShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 48,
  },
  // user-defined code here
  {
    title: "设备",
    dataIndex: "equipment",
    isShow: true,
    ellipses: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
    renderText: (item, record, index) => item?.name ?? "",
  },
  {
    title: "设备类型",
    dataIndex: "equipmentCategory",
    isShow: true,
    ellipses: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
    renderText: (item, record, index) => item?.name ?? "",
  },
  {
    title: "登记人",
    dataIndex: "registrationPerson",
    isShow: true,
    ellipses: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
    renderText: (item, record, index) => item?.name ?? "",
  },
  {
    title: "登记时间",
    dataIndex: "registrationTime",
    isShow: true,
    ellipses: true,
    render: (item) => {
      const content = formatDate(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
    renderText: (item) => formatDate(item) || "",
  },
  {
    title: "实际停用时间",
    dataIndex: "stopTime",
    isShow: true,
    ellipses: true,
    render: (item) => {
      const content = formatDate(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
    renderText: (item) => formatDate(item) || "",
  },
  {
    title: "停用类型",
    dataIndex: "stopType",
    isShow: true,
    ellipses: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(EQUIPMENT_STOPTYPE_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
    renderText: (item) => {
      const i = find(propEq(item, "id"))(EQUIPMENT_STOPTYPE_MAP);
      return i?.name ?? "-";
    },
  },
  {
    title: "计划启用时间",
    dataIndex: "planStartTime",
    isShow: true,
    ellipses: true,
    render: (item) => {
      const content = formatDate(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
    renderText: (item) => formatDate(item) || "",
  },
  {
    title: "实际启用时间",
    dataIndex: "startTime",
    isShow: true,
    ellipses: true,
    render: (item) => {
      const content = formatDate(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
    renderText: (item) => formatDate(item) || "",
  },
  {
    title: "上报状态",
    dataIndex: "reportStatus",
    isShow: false,
    ellipsis: true,
    width: 150,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(JOB_REPORT_STATUS_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
    renderText: (item, record, index) => {
      const i = find(propEq(item, "id"))(JOB_REPORT_STATUS_MAP);
      return i?.name ?? "-";
    },
  },
  {
    title: "上报结果",
    dataIndex: "reportResult",
    isShow: false,
    ellipsis: true,
    width: 150,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: "上报时间",
    dataIndex: "reportTime",
    isShow: false,
    ellipsis: true,
    width: 200,
    render: (item) => {
      const content = formatDate(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
    renderText: (text) => formatDate(text),
  },
];

const equipmentManagementStopExtendColumns = [
  // user-defined code here
];

export const equipmentManagementStopShowColumnsAtom = atom(
  equipmentManagementStopShowColumns
);

export const equipmentManagementStopColumnsAtom = atom([
  ...equipmentManagementStopShowColumns,
  ...equipmentManagementStopExtendColumns,
]);

/*export const equipmentManagementStopColumnsAtom = atom(
  (get) => get(equipmentManagementStopShowColumnsAtom).concat(get(equipmentManagementStopExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(equipmentManagementStopShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(equipmentManagementStopExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const equipmentManagementStopAtoms: CommonAtoms = {
  entity: "EquipmentManagementStop",
  entityCName: "停用设备",
  filter: equipmentManagementStopFilterAtom,
  Fn: equipmentManagementStopFnAtom,
  editModal: equipmentManagementStopEditModalAtom,
  configModal: equipmentManagementStopConfigModalAtom,
  columns: equipmentManagementStopColumnsAtom,
};
