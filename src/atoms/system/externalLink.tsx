import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { CommonAtoms } from "types";

export const systemExternalLinkFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const systemExternalLinkFnAtom = atom({
  refetch: () => {},
});

export const systemExternalLinkEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const systemExternalLinkConfigModalAtom = atom(false);

const systemExternalLinkShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 48,
  },
  // user-defined code here
  {
    title: "名称",
    dataIndex: "name",
    isShow: true,
  },
  {
    title: "地址",
    dataIndex: "url",
    isShow: true,
  },
];

const systemExternalLinkExtendColumns = [
  // user-defined code here
];

export const systemExternalLinkShowColumnsAtom = atom(
  systemExternalLinkShowColumns
);

export const systemExternalLinkColumnsAtom = atom([
  ...systemExternalLinkShowColumns,
  ...systemExternalLinkExtendColumns,
]);

/*export const systemExternalLinkColumnsAtom = atom(
  (get) => get(systemExternalLinkShowColumnsAtom).concat(get(systemExternalLinkExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(systemExternalLinkShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(systemExternalLinkExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const systemExternalLinkAtoms: CommonAtoms = {
  entity: "SystemExternalLink",
  filter: systemExternalLinkFilterAtom,
  Fn: systemExternalLinkFnAtom,
  editModal: systemExternalLinkEditModalAtom,
  configModal: systemExternalLinkConfigModalAtom,
  columns: systemExternalLinkColumnsAtom,
};
