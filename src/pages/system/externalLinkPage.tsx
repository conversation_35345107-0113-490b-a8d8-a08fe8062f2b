/* import { <PERSON>Filter, RoleContent, RoleSide } from './content'
import { RoleModal } from './modal'
 */

import {
  SystemExternalLinkContent,
  SystemExternalLinkFilter,
} from "./content";
import { SystemExternalLinkModal } from "./modal/externalLinkModal";

export function SystemExternalLinkPage({
  readonly = false,
  filter = {},
  ...restProps
}) {
  return (
    <div className="flex flex-col gap-4 ">
      {/* <RoleSide />
      <RoleModal />
      <RoleFilter />
      <RoleContent /> */}
      <SystemExternalLinkFilter filter={filter} />
      <SystemExternalLinkModal />
      <SystemExternalLinkContent
        readonly={readonly}
        filter={filter}
        {...restProps}
      />
    </div>
  );
}
