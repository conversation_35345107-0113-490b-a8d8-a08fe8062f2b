import { useQueryClient } from "@tanstack/react-query";
import { systemExternalLinkApis } from "api";
import { systemExternalLinkAtoms } from "atoms";
import { ExportProvider, List } from "components";
import React, { FC } from "react";

type SystemExternalLinkContentProps = {
  callback?: any;
  layout?: string;
  referAtom?: any;
  readonly?: boolean;
  filter?: any;
};
export const SystemExternalLinkContent: FC<
  SystemExternalLinkContentProps
> = ({
  callback,
  layout,
  referAtom,
  readonly = false,
  filter = {},
  ...restProps
}) => {
  if (layout === "modal") {
    readonly = true;
  }
  const queryClient = useQueryClient();
  const queryKey = "list" + systemExternalLinkAtoms.entity;

  return (
    <ExportProvider>
      <List
        atoms={systemExternalLinkAtoms}
        apis={systemExternalLinkApis}
        // operations, dynamicOperationFuncs
        filter={filter}
        callback={callback}
        referAtom={referAtom}
        layout={layout}
        readonly={readonly}
        {...restProps}
      />
    </ExportProvider>
  );
};
