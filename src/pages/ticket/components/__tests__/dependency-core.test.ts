import { beforeEach, describe, expect, it } from "vitest";
import { DependencyManager } from "../../utils/formDependencyManager";

/**
 * 核心依赖功能测试 - 不涉及UI组件
 * 专门测试两个核心问题的底层逻辑
 */

describe("依赖字段核心功能测试", () => {
  let dependencyManager: DependencyManager;

  const mockFormFields = [
    {
      itemId: "unitCategory",
      compType: "radio",
      compName: "作业单位类别",
      business: "unitCategory",
      formData: {
        formName: "作业单位类别",
        candidateList: [
          { id: 1, label: "本厂" },
          { id: 2, label: "承包商" },
        ],
      },
      children: [],
    },
    {
      itemId: "workLevel",
      compType: "radio",
      compName: "作业级别",
      business: "workLevel",
      formData: {
        formName: "作业级别",
        candidateList: [
          { id: "level1", label: "一级作业" },
          { id: "level2", label: "二级作业" },
          { id: "level3", label: "三级作业" },
        ],
      },
      children: [],
    },
    {
      itemId: "dependentTextField",
      compType: "input",
      compName: "依赖文本字段",
      business: "",
      formData: {
        formName: "依赖文本字段",
        dependent: "unitCategory",
        dependentValue: 1, // 只有当作业单位类别=1时才显示
      },
      children: [],
    },
    {
      itemId: "levelDependentField",
      compType: "input",
      compName: "级别依赖字段",
      business: "",
      formData: {
        formName: "级别依赖字段",
        dependent: "workLevel",
        dependentValue: "level2", // 只有当作业级别=level2时才显示
      },
      children: [],
    },
  ];

  beforeEach(() => {
    dependencyManager = new DependencyManager(mockFormFields);
  });

  /**
   * 问题1相关测试: 依赖字段配置功能
   */
  describe("问题1: 依赖字段配置功能", () => {
    it("应该正确识别单选字段作为可选的依赖字段", () => {
      const radioFields = mockFormFields.filter(
        (field) => field.compType === "radio"
      );

      // 验证有两个单选字段可作为依赖选项
      expect(radioFields).toHaveLength(2);
      expect(radioFields[0].itemId).toBe("unitCategory");
      expect(radioFields[1].itemId).toBe("workLevel");
    });

    it("应该正确获取依赖字段的候选值选项", () => {
      // 获取作业单位类别的候选值
      const unitCategoryField = mockFormFields.find(
        (field) => field.itemId === "unitCategory"
      );
      const candidateList = unitCategoryField?.formData?.candidateList;

      expect(candidateList).toEqual([
        { id: 1, label: "本厂" },
        { id: 2, label: "承包商" },
      ]);
    });

    it("应该正确获取作业级别的候选值选项", () => {
      // 获取作业级别的候选值
      const workLevelField = mockFormFields.find(
        (field) => field.itemId === "workLevel"
      );
      const candidateList = workLevelField?.formData?.candidateList;

      expect(candidateList).toEqual([
        { id: "level1", label: "一级作业" },
        { id: "level2", label: "二级作业" },
        { id: "level3", label: "三级作业" },
      ]);
    });

    it("应该正确识别字段的依赖关系配置", () => {
      const textFieldDependency =
        dependencyManager.getFieldDependency("dependentTextField");
      expect(textFieldDependency).toEqual({
        dependent: "unitCategory",
        dependentValue: 1,
      });

      const levelFieldDependency = dependencyManager.getFieldDependency(
        "levelDependentField"
      );
      expect(levelFieldDependency).toEqual({
        dependent: "workLevel",
        dependentValue: "level2",
      });
    });
  });

  /**
   * 问题2相关测试: 字段显隐逻辑
   */
  describe("问题2: 字段显隐逻辑", () => {
    it("初始状态下，依赖字段应该被隐藏", () => {
      const shouldShowTextField =
        dependencyManager.shouldShowField("dependentTextField");
      const shouldShowLevelField = dependencyManager.shouldShowField(
        "levelDependentField"
      );

      expect(shouldShowTextField).toBe(false);
      expect(shouldShowLevelField).toBe(false);
    });

    it("当被依赖字段值满足条件时，依赖字段应该显示", () => {
      // 设置作业单位类别为1（本厂）
      dependencyManager.updateFieldValue("unitCategory", 1);

      const shouldShowTextField =
        dependencyManager.shouldShowField("dependentTextField");
      expect(shouldShowTextField).toBe(true);

      // 设置作业级别为level2
      dependencyManager.updateFieldValue("workLevel", "level2");

      const shouldShowLevelField = dependencyManager.shouldShowField(
        "levelDependentField"
      );
      expect(shouldShowLevelField).toBe(true);
    });

    it("当被依赖字段值不满足条件时，依赖字段应该隐藏", () => {
      // 设置作业单位类别为2（承包商）- 不满足依赖条件1
      dependencyManager.updateFieldValue("unitCategory", 2);

      const shouldShowTextField =
        dependencyManager.shouldShowField("dependentTextField");
      expect(shouldShowTextField).toBe(false);

      // 设置作业级别为level1 - 不满足依赖条件level2
      dependencyManager.updateFieldValue("workLevel", "level1");

      const shouldShowLevelField = dependencyManager.shouldShowField(
        "levelDependentField"
      );
      expect(shouldShowLevelField).toBe(false);
    });

    it("当被依赖字段值发生改变时，应该实时更新依赖字段的显隐状态", () => {
      // 初始状态：设置为满足条件的值
      dependencyManager.updateFieldValue("unitCategory", 1);
      expect(dependencyManager.shouldShowField("dependentTextField")).toBe(
        true
      );

      // 改变为不满足条件的值
      dependencyManager.updateFieldValue("unitCategory", 2);
      expect(dependencyManager.shouldShowField("dependentTextField")).toBe(
        false
      );

      // 再次改变为满足条件的值
      dependencyManager.updateFieldValue("unitCategory", 1);
      expect(dependencyManager.shouldShowField("dependentTextField")).toBe(
        true
      );
    });

    it("应该正确处理数字类型的依赖值比较", () => {
      // 测试数字1与数字1的严格相等比较
      dependencyManager.updateFieldValue("unitCategory", 1);
      expect(dependencyManager.shouldShowField("dependentTextField")).toBe(
        true
      );

      // 测试数字2与数字1的不相等比较
      dependencyManager.updateFieldValue("unitCategory", 2);
      expect(dependencyManager.shouldShowField("dependentTextField")).toBe(
        false
      );
    });

    it("应该正确处理字符串类型的依赖值比较", () => {
      // 测试字符串"level2"与字符串"level2"的比较
      dependencyManager.updateFieldValue("workLevel", "level2");
      expect(dependencyManager.shouldShowField("levelDependentField")).toBe(
        true
      );

      // 测试字符串"level1"与字符串"level2"的比较
      dependencyManager.updateFieldValue("workLevel", "level1");
      expect(dependencyManager.shouldShowField("levelDependentField")).toBe(
        false
      );
    });

    it("应该正确处理多个独立的依赖关系", () => {
      // 设置两个不同的依赖字段值
      dependencyManager.updateFieldValue("unitCategory", 1); // 满足dependentTextField的条件
      dependencyManager.updateFieldValue("workLevel", "level2"); // 满足levelDependentField的条件

      // 两个依赖字段都应该显示
      expect(dependencyManager.shouldShowField("dependentTextField")).toBe(
        true
      );
      expect(dependencyManager.shouldShowField("levelDependentField")).toBe(
        true
      );

      // 改变其中一个依赖值
      dependencyManager.updateFieldValue("unitCategory", 2); // 不满足dependentTextField的条件

      // 只有对应的依赖字段被隐藏
      expect(dependencyManager.shouldShowField("dependentTextField")).toBe(
        false
      );
      expect(dependencyManager.shouldShowField("levelDependentField")).toBe(
        true
      ); // 不受影响
    });

    it("应该返回正确的受影响字段列表", () => {
      const affectedFields = dependencyManager.updateFieldValue(
        "unitCategory",
        1
      );

      // 应该包含依赖于unitCategory的字段
      expect(affectedFields).toContain("dependentTextField");
      expect(affectedFields).not.toContain("levelDependentField"); // 不依赖于unitCategory
    });
  });

  /**
   * 边界情况测试
   */
  describe("边界情况测试", () => {
    it("应该正确处理undefined依赖值", () => {
      dependencyManager.updateFieldValue("unitCategory", undefined);
      expect(dependencyManager.shouldShowField("dependentTextField")).toBe(
        false
      );
    });

    it("应该正确处理null依赖值", () => {
      dependencyManager.updateFieldValue("unitCategory", null);
      expect(dependencyManager.shouldShowField("dependentTextField")).toBe(
        false
      );
    });

    it("应该正确处理空字符串依赖值", () => {
      dependencyManager.updateFieldValue("unitCategory", "");
      expect(dependencyManager.shouldShowField("dependentTextField")).toBe(
        false
      );
    });

    it("应该正确处理0值依赖", () => {
      // 创建一个依赖值为0的字段配置
      const fieldsWithZeroValue = [
        ...mockFormFields,
        {
          itemId: "zeroValueField",
          compType: "input",
          compName: "零值依赖字段",
          formData: {
            formName: "零值依赖字段",
            dependent: "unitCategory",
            dependentValue: 0, // 依赖值为0
          },
          children: [],
        },
      ];

      const manager = new DependencyManager(fieldsWithZeroValue);
      manager.updateFieldValue("unitCategory", 0);
      expect(manager.shouldShowField("zeroValueField")).toBe(true);
    });

    it("应该正确处理无依赖关系的字段", () => {
      const shouldShowUnitCategory =
        dependencyManager.shouldShowField("unitCategory");
      const shouldShowWorkLevel =
        dependencyManager.shouldShowField("workLevel");

      // 无依赖关系的字段应该始终显示（返回true或不进行隐藏处理）
      expect(shouldShowUnitCategory).toBe(true);
      expect(shouldShowWorkLevel).toBe(true);
    });

    it("应该正确处理不存在的字段ID", () => {
      const shouldShowNonExistent =
        dependencyManager.shouldShowField("nonExistentField");

      // 不存在的字段应该返回true（默认显示）
      expect(shouldShowNonExistent).toBe(true);
    });

    it("应该正确处理批量字段值更新", () => {
      const fieldValues = {
        unitCategory: 1,
        workLevel: "level2",
      };

      dependencyManager.batchUpdateFieldValues(fieldValues);

      // 验证批量更新后的字段可见性
      expect(dependencyManager.shouldShowField("dependentTextField")).toBe(
        true
      );
      expect(dependencyManager.shouldShowField("levelDependentField")).toBe(
        true
      );
    });
  });

  /**
   * 性能和稳定性测试
   */
  describe("性能和稳定性测试", () => {
    it("应该能处理大量字段值更新", () => {
      // 模拟大量字段值更新
      for (let i = 0; i < 100; i++) {
        dependencyManager.updateFieldValue("unitCategory", i % 2 === 0 ? 1 : 2);
      }

      // 最终状态应该正确
      expect(dependencyManager.shouldShowField("dependentTextField")).toBe(
        false
      ); // 最后设置为2
    });

    it("应该正确处理循环依赖检测", () => {
      // 创建可能的循环依赖场景
      const circularFields = [
        {
          itemId: "fieldA",
          compType: "radio",
          compName: "字段A",
          formData: {
            candidateList: [{ id: 1, label: "选项1" }],
            dependent: "fieldB",
            dependentValue: 1,
          },
          children: [],
        },
        {
          itemId: "fieldB",
          compType: "radio",
          compName: "字段B",
          formData: {
            candidateList: [{ id: 1, label: "选项1" }],
            dependent: "fieldA",
            dependentValue: 1,
          },
          children: [],
        },
      ];

      // 应该能够创建管理器而不崩溃
      const circularManager = new DependencyManager(circularFields);
      expect(circularManager).toBeDefined();

      // 应该能够处理字段值更新而不进入无限循环
      circularManager.updateFieldValue("fieldA", 1);
      expect(circularManager.shouldShowField("fieldB")).toBeDefined();
    });
  });
});
