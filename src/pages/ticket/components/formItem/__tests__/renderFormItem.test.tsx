import { render } from "@testing-library/react";
import { beforeEach, describe, expect, it, vi } from "vitest";

// <PERSON><PERSON>act hooks properly
vi.mock("react", async () => {
  const actual = await vi.importActual("react");
  return {
    ...actual,
    useMemo: vi.fn((fn) => fn()),
    useCallback: vi.fn((fn) => fn),
    useEffect: vi.fn(),
    useState: vi.fn(() => [null, vi.fn()]),
  };
});

// Mock tdesign-react components with comprehensive coverage
vi.mock("tdesign-react", () => ({
  Form: {
    FormItem: ({ children, label, rules, labelAlign, className }: any) => (
      <div
        data-testid="form-item"
        data-label-align={labelAlign}
        className={className}
        data-required={rules?.[0]?.required}
      >
        <label>{label}</label>
        {children}
      </div>
    ),
  },
  Input: ({ placeholder, disabled, readonly }: any) => (
    <input
      data-testid="input"
      placeholder={placeholder}
      disabled={disabled}
      readOnly={readonly}
    />
  ),
  Select: ({ placeholder }: any) => (
    <select data-testid="select" placeholder={placeholder}>
      <option>请选择</option>
    </select>
  ),
  Textarea: ({ placeholder }: any) => (
    <textarea data-testid="textarea" placeholder={placeholder} />
  ),
  DatePicker: ({ placeholder, disabled, className }: any) => (
    <input
      type="date"
      data-testid="date-picker"
      placeholder={placeholder}
      disabled={disabled}
      className={className}
    />
  ),
  Checkbox: ({ children, value }: any) => (
    <label data-testid="checkbox">
      <input type="checkbox" value={value} />
      {children}
    </label>
  ),
  Radio: Object.assign(
    ({ children, value }: any) => (
      <label data-testid="radio">
        <input type="radio" value={value} />
        {children}
      </label>
    ),
    {
      Group: ({ children, defaultValue, disabled }: any) => (
        <div
          data-testid="radio-group"
          data-default-value={defaultValue}
          data-disabled={disabled}
        >
          {children}
        </div>
      ),
    }
  ),
  Switch: ({ size }: any) => <div data-testid="switch" data-size={size} />,
  Upload: ({ children }: any) => <div data-testid="upload">{children}</div>,
}));

// Mock Semi UI components
vi.mock("@douyinfe/semi-icons", async () => {
  const actual = await vi.importActual("@douyinfe/semi-icons");
  return {
    ...actual,
    IconCustomize: () => <div data-testid="icon-customize" />,
    IconCloud: () => <div data-testid="icon-cloud" />,
  };
});

// Mock EventCover component with proper props handling
vi.mock("../eventCover", () => ({
  __esModule: true,
  default: ({ children, eventData, noMask }: any) => (
    <div
      data-testid="event-cover"
      data-no-mask={noMask}
      data-event-data={JSON.stringify(eventData)}
    >
      {children}
    </div>
  ),
}));

// Mock FormTable component to avoid Context dependency
vi.mock("../lib/formTable", () => ({
  FormTable: ({ children, ...props }: any) => (
    <div data-testid="form-table" {...props}>
      {children}
    </div>
  ),
}));

// Mock FormContext and related utilities
vi.mock("../../utils/contexts", () => ({
  FormContext: {
    Provider: ({ children }: any) => children,
    Consumer: ({ children }: any) =>
      children({ containerState: { value: [] } }),
  },
}));

// Mock Jotai atoms
vi.mock("jotai", () => ({
  useAtom: vi.fn(() => [null, vi.fn()]),
  atom: vi.fn(() => ({})),
}));

// Mock FormContext
vi.mock("../../utils/contexts", () => ({
  FormContext: {
    Provider: ({ children }: any) => children,
    Consumer: ({ children }: any) =>
      children({ containerState: { value: [] } }),
  },
}));

// Mock DependencyManager
vi.mock("../../utils/formDependencyManager", () => ({
  DependencyManager: vi.fn().mockImplementation(() => ({
    shouldShowField: vi.fn(() => true),
    getFieldValue: vi.fn(() => ""),
    updateFieldValue: vi.fn(),
    batchUpdateFieldValues: vi.fn(),
    getAffectedFields: vi.fn(() => []),
    getFormFields: vi.fn(() => []),
  })),
}));

// Mock Jotai atoms
vi.mock("jotai", () => ({
  atom: vi.fn(() => ({ init: null })),
  useAtom: vi.fn(() => [null, vi.fn()]),
  useAtomValue: vi.fn(() => null),
  useSetAtom: vi.fn(() => vi.fn()),
}));

// Import the actual component after mocks
import renderFormItem from "../index";

describe("renderFormItem Component", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("Basic Functionality", () => {
    it("should import renderFormItem function", () => {
      expect(typeof renderFormItem).toBe("function");
    });

    it("should handle empty array input", () => {
      const result = renderFormItem([], {});
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(0);
    });

    it("should handle null input", () => {
      const result = renderFormItem(null as any, {});
      expect(result).toBeFalsy();
    });

    it("should handle undefined input", () => {
      const result = renderFormItem(undefined as any, {});
      expect(result).toBeFalsy();
    });
  });

  describe("Component Type Rendering", () => {
    it("should render wrap component", () => {
      const mockData = [
        {
          itemId: "wrap-1",
          compType: "wrap",
          compName: "包装组件",
          formData: {
            formName: "测试包装",
          },
        },
      ];

      const mockRenderChild = vi.fn(() => (
        <div data-testid="child-content">child content</div>
      ));
      const result = renderFormItem(mockData, { renderChild: mockRenderChild });

      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(1);

      // Render the result to test it
      const { container } = render(<div>{result}</div>);

      // Check if EventCover is rendered or at least the wrap component structure
      const eventCover = container.querySelector('[data-testid="event-cover"]');
      const childContent = container.querySelector(
        '[data-testid="child-content"]'
      );

      // Either EventCover should be found, or the child content should be rendered
      expect(eventCover || childContent).toBeTruthy();

      // Verify renderChild was called
      expect(mockRenderChild).toHaveBeenCalledWith(mockData[0]);
    });

    it("should render input component", () => {
      const mockData = [
        {
          itemId: "input-1",
          compType: "input",
          compName: "输入框",
          formData: {
            formName: "测试输入",
            placeHolder: "请输入内容",
          },
        },
      ];

      const result = renderFormItem(mockData, {});
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(1);

      // Render the result to test it
      const { container } = render(<div>{result}</div>);
      expect(
        container.querySelector('[data-testid="form-item"]')
      ).toBeInTheDocument();
      expect(
        container.querySelector('[data-testid="input"]')
      ).toBeInTheDocument();
    });

    it("should render selector component", () => {
      const mockData = [
        {
          itemId: "select-1",
          compType: "selector",
          compName: "选择器",
          formData: {
            formName: "测试选择",
            placeHolder: "请选择",
          },
        },
      ];

      const result = renderFormItem(mockData, {});
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(1);

      // Render the result to test it
      const { container } = render(<div>{result}</div>);
      expect(
        container.querySelector('[data-testid="form-item"]')
      ).toBeInTheDocument();
      expect(
        container.querySelector('[data-testid="select"]')
      ).toBeInTheDocument();
    });

    it("should render textarea component", () => {
      const mockData = [
        {
          itemId: "textarea-1",
          compType: "textarea",
          compName: "文本域",
          formData: {
            formName: "测试文本域",
            placeHolder: "请输入详细内容",
          },
        },
      ];

      const result = renderFormItem(mockData, {});
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(1);

      // Render the result to test it
      const { container } = render(<div>{result}</div>);
      expect(
        container.querySelector('[data-testid="form-item"]')
      ).toBeInTheDocument();
      expect(
        container.querySelector('[data-testid="textarea"]')
      ).toBeInTheDocument();
    });

    it("should render datePicker component", () => {
      const mockData = [
        {
          itemId: "date-1",
          compType: "datePicker",
          compName: "日期选择器",
          formData: {
            formName: "测试日期",
            placeHolder: "请选择日期",
          },
        },
      ];

      const result = renderFormItem(mockData, {});
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(1);

      // Render the result to test it
      const { container } = render(<div>{result}</div>);
      expect(
        container.querySelector('[data-testid="form-item"]')
      ).toBeInTheDocument();
      expect(
        container.querySelector('[data-testid="date-picker"]')
      ).toBeInTheDocument();
    });

    it("should render radio component with options", () => {
      const mockData = [
        {
          itemId: "radio-1",
          compType: "radio",
          compName: "单选框",
          formData: {
            formName: "测试单选",
            candidateList: [
              { id: "1", label: "选项1" },
              { id: "2", label: "选项2" },
            ],
          },
        },
      ];

      const result = renderFormItem(mockData, {});
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(1);

      // Render the result to test it
      const { container } = render(<div>{result}</div>);
      expect(
        container.querySelector('[data-testid="form-item"]')
      ).toBeInTheDocument();
      expect(
        container.querySelector('[data-testid="radio-group"]')
      ).toBeInTheDocument();
    });

    it("should render radio component without options", () => {
      const mockData = [
        {
          itemId: "radio-2",
          compType: "radio",
          compName: "空单选框",
          formData: {
            formName: "测试空单选",
            candidateList: [],
          },
        },
      ];

      const result = renderFormItem(mockData, {});
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(1);

      // Render the result to test it
      const { container } = render(<div>{result}</div>);
      expect(
        container.querySelector('[data-testid="form-item"]')
      ).toBeInTheDocument();
      expect(container.textContent).toContain("添加自定义选项");
    });

    it("should render checkbox component with options", () => {
      const mockData = [
        {
          itemId: "checkbox-1",
          compType: "checkbox",
          compName: "复选框",
          formData: {
            formName: "测试复选",
            candidateList: [{ option: "选项1" }, { option: "选项2" }],
          },
        },
      ];

      const result = renderFormItem(mockData, {});
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(1);

      // Render the result to test it
      const { container } = render(<div>{result}</div>);
      expect(
        container.querySelector('[data-testid="form-item"]')
      ).toBeInTheDocument();
      expect(
        container.querySelectorAll('[data-testid="checkbox"]')
      ).toHaveLength(2);
    });

    it("should render checkbox component without options", () => {
      const mockData = [
        {
          itemId: "checkbox-2",
          compType: "checkbox",
          compName: "空复选框",
          formData: {
            formName: "测试空复选",
            candidateList: [],
          },
        },
      ];

      const result = renderFormItem(mockData, {});
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(1);

      // Render the result to test it
      const { container } = render(<div>{result}</div>);
      expect(
        container.querySelector('[data-testid="form-item"]')
      ).toBeInTheDocument();
      expect(container.textContent).toContain("添加自定义选项");
    });

    it("should render switch component", () => {
      const mockData = [
        {
          itemId: "switch-1",
          compType: "switch",
          compName: "开关",
          formData: {
            formName: "测试开关",
          },
        },
      ];

      const result = renderFormItem(mockData, {});
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(1);

      // Render the result to test it
      const { container } = render(<div>{result}</div>);
      expect(
        container.querySelector('[data-testid="form-item"]')
      ).toBeInTheDocument();
      expect(
        container.querySelector('[data-testid="switch"]')
      ).toBeInTheDocument();
    });

    it("should render employeePicker component", () => {
      const mockData = [
        {
          itemId: "employee-1",
          compType: "employeePicker",
          compName: "员工选择器",
          formData: {
            formName: "测试员工选择",
          },
        },
      ];

      const result = renderFormItem(mockData, {});
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(1);

      // Render the result to test it
      const { container } = render(<div>{result}</div>);
      expect(
        container.querySelector('[data-testid="form-item"]')
      ).toBeInTheDocument();
      expect(
        container.querySelector('[data-testid="input"]')
      ).toBeInTheDocument();
    });

    it("should render protectivePicker component", () => {
      const mockData = [
        {
          itemId: "protective-1",
          compType: "protectivePicker",
          compName: "防护用品选择器",
          formData: {
            formName: "测试防护用品",
          },
        },
      ];

      const result = renderFormItem(mockData, {});
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(1);

      // Render the result to test it
      const { container } = render(<div>{result}</div>);
      expect(
        container.querySelector('[data-testid="form-item"]')
      ).toBeInTheDocument();
      expect(
        container.querySelector('[data-testid="input"]')
      ).toBeInTheDocument();
    });

    it("should render dhSelectPicker component", () => {
      const mockData = [
        {
          itemId: "dh-1",
          compType: "dhSelectPicker",
          compName: "动火方式选择器",
          formData: {
            formName: "测试动火方式",
          },
        },
      ];

      const result = renderFormItem(mockData, {});
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(1);

      // Render the result to test it
      const { container } = render(<div>{result}</div>);
      expect(
        container.querySelector('[data-testid="form-item"]')
      ).toBeInTheDocument();
      expect(
        container.querySelector('[data-testid="input"]')
      ).toBeInTheDocument();
    });
  });

  describe("Advanced Component Testing", () => {
    it("should handle multiple items with different types", () => {
      const mockData = [
        {
          itemId: "multi-1",
          compType: "input",
          compName: "输入框",
          formData: { formName: "输入框" },
        },
        {
          itemId: "multi-2",
          compType: "selector",
          compName: "选择器",
          formData: { formName: "选择器" },
        },
        {
          itemId: "multi-3",
          compType: "textarea",
          compName: "文本域",
          formData: { formName: "文本域" },
        },
      ];

      const result = renderFormItem(mockData, {});
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(3);

      // Render the result to test it
      const { container } = render(<div>{result}</div>);
      expect(
        container.querySelectorAll('[data-testid="form-item"]')
      ).toHaveLength(3);
    });

    it("should handle parent parameter", () => {
      const mockData = [
        {
          itemId: "parent-1",
          compType: "input",
          compName: "子项输入框",
          formData: { formName: "子项输入框" },
        },
      ];

      const mockParent = { id: "parent", name: "父级组件" };
      const result = renderFormItem(mockData, { parent: mockParent });

      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(1);
    });

    it("should handle renderChild function for wrap component", () => {
      const mockData = [
        {
          itemId: "wrap-child-1",
          compType: "wrap",
          compName: "包装组件",
          formData: { formName: "包装组件" },
        },
      ];

      const mockRenderChild = vi.fn(() => (
        <div data-testid="custom-child">自定义子组件</div>
      ));
      const result = renderFormItem(mockData, { renderChild: mockRenderChild });

      expect(result).toBeDefined();
      expect(mockRenderChild).toHaveBeenCalledWith(mockData[0]);

      // Render the result to test it
      const { container } = render(<div>{result}</div>);
      expect(
        container.querySelector('[data-testid="custom-child"]')
      ).toBeInTheDocument();
    });

    it("should handle required field validation rules", () => {
      const mockData = [
        {
          itemId: "required-1",
          compType: "input",
          compName: "必填输入框",
          formData: {
            formName: "必填输入框",
            isReq: "required",
          },
        },
      ];

      const result = renderFormItem(mockData, {});
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(1);

      // Render the result to test it
      const { container } = render(<div>{result}</div>);
      const formItem = container.querySelector('[data-testid="form-item"]');
      expect(formItem).toHaveAttribute("data-required", "true");
    });

    it("should handle optional field validation rules", () => {
      const mockData = [
        {
          itemId: "optional-1",
          compType: "input",
          compName: "可选输入框",
          formData: {
            formName: "可选输入框",
            isReq: "optional",
          },
        },
      ];

      const result = renderFormItem(mockData, {});
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(1);

      // Render the result to test it
      const { container } = render(<div>{result}</div>);
      const formItem = container.querySelector('[data-testid="form-item"]');
      expect(formItem).toHaveAttribute("data-required", "false");
    });

    it("should handle missing formData", () => {
      const mockData = [
        {
          itemId: "no-data-1",
          compType: "input",
          compName: "无数据输入框",
          // formData is missing
        },
      ];

      const result = renderFormItem(mockData, {});
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(1);

      // Should not throw error
      expect(() => render(<div>{result}</div>)).not.toThrow();
    });

    it("should handle empty candidateList for radio", () => {
      const mockData = [
        {
          itemId: "empty-radio-1",
          compType: "radio",
          compName: "空选项单选框",
          formData: {
            formName: "空选项单选框",
            candidateList: null,
          },
        },
      ];

      const result = renderFormItem(mockData, {});
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(1);

      // Render the result to test it
      const { container } = render(<div>{result}</div>);
      expect(container.textContent).toContain("添加自定义选项");
    });

    it("should handle empty candidateList for checkbox", () => {
      const mockData = [
        {
          itemId: "empty-checkbox-1",
          compType: "checkbox",
          compName: "空选项复选框",
          formData: {
            formName: "空选项复选框",
            candidateList: null,
          },
        },
      ];

      const result = renderFormItem(mockData, {});
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(1);

      // Render the result to test it
      const { container } = render(<div>{result}</div>);
      expect(container.textContent).toContain("添加自定义选项");
    });

    it("should render plainText component", () => {
      const mockData = [
        {
          itemId: "plain-1",
          compType: "plainText",
          compName: "纯文本",
          formData: {
            formName: "纯文本",
            actualValue: "这是纯文本内容",
          },
        },
      ];

      const result = renderFormItem(mockData, {});
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(1);

      // Render the result to test it
      const { container } = render(<div>{result}</div>);
      expect(container.textContent).toContain("这是纯文本内容");
    });

    it("should render table component", () => {
      const mockData = [
        {
          itemId: "table-1",
          compType: "table",
          compName: "表格",
          formData: {
            formName: "测试表格",
          },
        },
      ];

      const result = renderFormItem(mockData, {});
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(1);

      // Render the result to test it - table component should work with mocked FormTable
      const { container } = render(<div>{result}</div>);

      expect(
        container.querySelector('[data-testid="form-table"]')
      ).toBeInTheDocument();
    });

    it("should handle unknown component type", () => {
      const mockData = [
        {
          itemId: "unknown-1",
          compType: "unknownType",
          compName: "未知组件",
          formData: {
            formName: "未知组件",
          },
        },
      ];

      const result = renderFormItem(mockData, {});
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(1);

      // Should render default input component
      const { container } = render(<div>{result}</div>);
      expect(
        container.querySelector('[data-testid="form-item"]')
      ).toBeInTheDocument();
      expect(
        container.querySelector('[data-testid="input"]')
      ).toBeInTheDocument();
    });

    it("should handle complex nested data structure", () => {
      const mockData = [
        {
          itemId: "complex-1",
          compType: "radio",
          compName: "复杂单选框",
          formData: {
            formName: "复杂单选框",
            candidateList: [
              {
                id: "complex1",
                label: "复杂选项1",
                metadata: { type: "primary", level: 1 },
              },
              {
                id: "complex2",
                label: "复杂选项2",
                metadata: { type: "secondary", level: 2 },
              },
            ],
            validation: {
              required: true,
              customRules: ["rule1", "rule2"],
            },
          },
        },
      ];

      const result = renderFormItem(mockData, {});
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(1);

      // Should handle complex data without errors
      expect(() => render(<div>{result}</div>)).not.toThrow();
    });
  });

  describe("Dependency Field Testing", () => {
    it("should render dependency field with proper styling", () => {
      const mockData = [
        {
          itemId: "dependent-1",
          compType: "input",
          compName: "依赖字段",
          formData: {
            formName: "依赖字段",
            dependent: "unitCategory",
            dependentValue: 2,
          },
        },
      ];

      const result = renderFormItem(mockData, {});
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(1);

      // Render the result to test it
      const { container } = render(<div>{result}</div>);

      // Should show dependency information
      expect(container.textContent).toContain("依赖:");

      // Should have proper styling for dependent field
      const fieldContainer = container.querySelector('[comp-type="input"]');
      expect(fieldContainer).toBeInTheDocument();
    });

    it("should show dependency indicator", () => {
      const mockData = [
        {
          itemId: "indicator-1",
          compType: "textarea",
          compName: "带指示器的依赖字段",
          formData: {
            formName: "带指示器的依赖字段",
            dependent: "workLevel",
            dependentValue: "level3",
          },
        },
      ];

      const result = renderFormItem(mockData, {});
      expect(result).toBeDefined();

      // Render the result to test it
      const { container } = render(<div>{result}</div>);

      // Should show animated indicator
      const indicator = container.querySelector(".animate-pulse");
      expect(indicator).toBeInTheDocument();

      // Should show dependency badge
      expect(container.textContent).toContain("依赖:");
      expect(container.textContent).toContain("level3");
    });

    it("should handle conditional display overlay", () => {
      const mockData = [
        {
          itemId: "overlay-1",
          compType: "input",
          compName: "条件显示字段",
          formData: {
            formName: "条件显示字段",
            dependent: "someField",
            dependentValue: "someValue",
          },
        },
      ];

      const result = renderFormItem(mockData, {});
      expect(result).toBeDefined();

      // Render the result to test it
      const { container } = render(<div>{result}</div>);

      // Should show conditional display overlay
      expect(container.textContent).toContain("条件显示字段");
    });
  });
});
