import { Table, Typography } from "@douyinfe/semi-ui";
import { useQuery } from "@tanstack/react-query";
import { jobSliceInfoAtom } from "atoms/specialWork";
import { getGasAnalysisTimeoutRecords } from "api/specialWork/jobSlice";
import { useAtom } from "jotai";
import { Empty } from "@douyinfe/semi-ui";
import { formatDate } from "utils";
import { JOB_STAGE_MAP } from "components/enum/specialWork";

const { Text } = Typography;

export const GasAnalysisTimeoutRecordsTab = () => {
  const [info] = useAtom(jobSliceInfoAtom);

  const { data, isLoading } = useQuery({
    queryKey: ["getGasAnalysisTimeoutRecords", info?.id],
    queryFn: () => getGasAnalysisTimeoutRecords(info?.id),
    enabled: !!info?.id,
  });

  const columns = [
    {
      title: "作业阶段",
      dataIndex: "stage",
      render: (stage: number) => {
        const stageInfo = JOB_STAGE_MAP.find(item => item.id === stage);
        return stageInfo ? <Text type={stageInfo.color as any}>{stageInfo.name}</Text> : "-";
      },
    },
    {
      title: "采样有效期(分钟)",
      dataIndex: "interval",
      render: (text: number) => text ?? "-",
    },
    {
      title: "上次采样时间",
      dataIndex: "lastSampleTime",
      render: (text: string) => formatDate(text) || "-",
    },
    {
      title: "超时时间",
      dataIndex: "duration",
      render: (text: string) => text || "-",
    },
  ];

  return (
    <div className="p-4">
      <Table
        columns={columns}
        dataSource={data?.data || []}
        loading={isLoading}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          pageSizeOpts: [10, 20, 50, 100],
        }}
        empty={<Empty description="暂无气体分析超时记录" />}
        rowKey="id"
      />
    </div>
  );
};
