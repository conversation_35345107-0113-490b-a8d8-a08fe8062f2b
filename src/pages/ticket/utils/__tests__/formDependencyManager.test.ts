import { ItemType } from "../../../../types/sendbox";
import { DependencyManager } from "../formDependencyManager";

// Mock form fields for testing
const mockFormFields: ItemType[] = [
  {
    compId: "1",
    itemId: "field1",
    compName: "作业单位类别",
    compType: "radio",
    business: "unitCategory",
    group: "base",
    formData: {
      formName: "作业单位类别",
      candidateList: [
        { id: 1, label: "本厂" },
        { id: 2, label: "承包商" },
      ],
    },
    children: [],
  },
  {
    compId: "2",
    itemId: "field2",
    compName: "MOC登记号",
    compType: "input",
    business: "mocNumber",
    group: "base",
    formData: {
      formName: "MOC登记号",
      dependent: "field1",
      dependentValue: 2,
    },
    children: [],
  },
  {
    compId: "3",
    itemId: "field3",
    compName: "普通字段",
    compType: "input",
    business: "normal",
    group: "base",
    formData: {
      formName: "普通字段",
    },
    children: [],
  },
];

describe("DependencyManager", () => {
  let manager: DependencyManager;

  beforeEach(() => {
    manager = new DependencyManager(mockFormFields);
  });

  describe("构建依赖映射", () => {
    test("应该正确构建依赖关系映射", () => {
      const dependentFields = manager.getDependentFields("field1");
      expect(dependentFields).toContain("field2");
    });
  });

  describe("字段可见性判断", () => {
    test("无依赖的字段应该默认显示", () => {
      expect(manager.shouldShowField("field1")).toBe(true);
      expect(manager.shouldShowField("field3")).toBe(true);
    });

    test("有依赖但未满足条件的字段应该隐藏", () => {
      expect(manager.shouldShowField("field2")).toBe(false);
    });

    test("有依赖且满足条件的字段应该显示", () => {
      manager.updateFieldValue("field1", 2);
      expect(manager.shouldShowField("field2")).toBe(true);
    });

    test("应该处理字符串和数字类型的值比较", () => {
      manager.updateFieldValue("field1", "2");
      expect(manager.shouldShowField("field2")).toBe(true);
    });
  });

  describe("字段值更新", () => {
    test("更新字段值应该返回受影响的字段", () => {
      const affectedFields = manager.updateFieldValue("field1", 2);
      expect(affectedFields).toContain("field2");
    });

    test("批量更新字段值应该返回所有受影响的字段", () => {
      const affectedFields = manager.batchUpdateFieldValues({
        field1: 2,
        field3: "test",
      });
      expect(affectedFields).toContain("field2");
    });
  });

  describe("获取隐藏和可见字段", () => {
    test("应该正确返回隐藏的字段列表", () => {
      const hiddenFields = manager.getHiddenFields();
      expect(hiddenFields).toContain("field2");
    });

    test("应该正确返回可见的字段列表", () => {
      const visibleFields = manager.getVisibleFields();
      expect(visibleFields).toContain("field1");
      expect(visibleFields).toContain("field3");
      expect(visibleFields).not.toContain("field2");
    });

    test("满足依赖条件后字段应该变为可见", () => {
      manager.updateFieldValue("field1", 2);
      const visibleFields = manager.getVisibleFields();
      expect(visibleFields).toContain("field2");
    });
  });

  describe("依赖关系验证", () => {
    test("有效的依赖配置应该通过验证", () => {
      const validation = manager.validateDependencyConfig();
      expect(validation.valid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    test("依赖不存在的字段应该返回错误", () => {
      const invalidFields: ItemType[] = [
        {
          compId: "1",
          itemId: "field1",
          compName: "测试字段",
          compType: "input",
          business: "test",
          group: "base",
          formData: {
            formName: "测试字段",
            dependent: "nonexistent",
            dependentValue: 1,
          },
          children: [],
        },
      ];

      const invalidManager = new DependencyManager(invalidFields);
      const validation = invalidManager.validateDependencyConfig();
      expect(validation.valid).toBe(false);
      expect(validation.errors.length).toBeGreaterThan(0);
      expect(validation.errors[0]).toContain("不存在");
    });

    test("依赖值不在候选列表中应该返回错误", () => {
      const invalidFields: ItemType[] = [
        {
          compId: "1",
          itemId: "field1",
          compName: "单选字段",
          compType: "radio",
          business: "radio",
          group: "base",
          formData: {
            formName: "单选字段",
            candidateList: [
              { id: 1, label: "选项1" },
              { id: 2, label: "选项2" },
            ],
          },
          children: [],
        },
        {
          compId: "2",
          itemId: "field2",
          compName: "依赖字段",
          compType: "input",
          business: "dependent",
          group: "base",
          formData: {
            formName: "依赖字段",
            dependent: "field1",
            dependentValue: 999, // 不存在的值
          },
          children: [],
        },
      ];

      const invalidManager = new DependencyManager(invalidFields);
      const validation = invalidManager.validateDependencyConfig();
      expect(validation.valid).toBe(false);
      expect(validation.errors.length).toBeGreaterThan(0);
      expect(validation.errors[0]).toContain("选项中不存在");
    });
  });

  describe("嵌套表单结构处理", () => {
    test("应该正确处理嵌套的表单结构", () => {
      const nestedFields: ItemType[] = [
        {
          compId: "1",
          itemId: "parent",
          compName: "父级字段",
          compType: "wrap",
          business: "parent",
          group: "layout",
          formData: {},
          children: [
            {
              compId: "2",
              itemId: "child1",
              compName: "子字段1",
              compType: "radio",
              business: "child1",
              group: "base",
              formData: {
                formName: "子字段1",
                candidateList: [
                  { id: 1, label: "选项1" },
                  { id: 2, label: "选项2" },
                ],
              },
              children: [],
            },
            {
              compId: "3",
              itemId: "child2",
              compName: "子字段2",
              compType: "input",
              business: "child2",
              group: "base",
              formData: {
                formName: "子字段2",
                dependent: "child1",
                dependentValue: 1,
              },
              children: [],
            },
          ],
        },
      ];

      const nestedManager = new DependencyManager(nestedFields);

      // 验证嵌套字段被正确扁平化
      expect(nestedManager.shouldShowField("child1")).toBe(true);
      expect(nestedManager.shouldShowField("child2")).toBe(false);

      // 验证依赖关系在嵌套结构中正常工作
      nestedManager.updateFieldValue("child1", 1);
      expect(nestedManager.shouldShowField("child2")).toBe(true);
    });
  });

  describe("工具方法", () => {
    test("应该正确获取字段的依赖信息", () => {
      const dependency = manager.getFieldDependency("field2");
      expect(dependency).toEqual({
        dependent: "field1",
        dependentValue: 2,
      });
    });

    test("无依赖字段应该返回null", () => {
      const dependency = manager.getFieldDependency("field1");
      expect(dependency).toBeNull();
    });

    test("应该能够重置字段值", () => {
      manager.updateFieldValue("field1", 2);
      manager.resetFieldValues();
      const allValues = manager.getAllFieldValues();
      expect(allValues.size).toBe(0);
    });

    test("应该能够获取所有字段值", () => {
      manager.updateFieldValue("field1", 2);
      manager.updateFieldValue("field3", "test");
      const allValues = manager.getAllFieldValues();
      expect(allValues.get("field1")).toBe(2);
      expect(allValues.get("field3")).toBe("test");
    });
  });

  describe("循环依赖检测", () => {
    test("应该检测到简单的循环依赖", () => {
      const circularFields: ItemType[] = [
        {
          compId: "1",
          itemId: "fieldA",
          compName: "字段A",
          compType: "radio",
          business: "fieldA",
          group: "base",
          formData: {
            formName: "字段A",
            dependent: "fieldB",
            dependentValue: 1,
            candidateList: [
              { id: 1, label: "选项1" },
              { id: 2, label: "选项2" },
            ],
          },
          children: [],
        },
        {
          compId: "2",
          itemId: "fieldB",
          compName: "字段B",
          compType: "radio",
          business: "fieldB",
          group: "base",
          formData: {
            formName: "字段B",
            dependent: "fieldA",
            dependentValue: 1,
            candidateList: [
              { id: 1, label: "选项1" },
              { id: 2, label: "选项2" },
            ],
          },
          children: [],
        },
      ];

      const circularManager = new DependencyManager(circularFields);
      const validation = circularManager.validateDependencyConfig();
      expect(validation.valid).toBe(false);
      expect(
        validation.errors.some((error) => error.includes("循环依赖"))
      ).toBe(true);
    });

    test("应该检测到复杂的循环依赖", () => {
      const complexCircularFields: ItemType[] = [
        {
          compId: "1",
          itemId: "fieldA",
          compName: "字段A",
          compType: "radio",
          business: "fieldA",
          group: "base",
          formData: {
            formName: "字段A",
            dependent: "fieldB",
            dependentValue: 1,
            candidateList: [{ id: 1, label: "选项1" }],
          },
          children: [],
        },
        {
          compId: "2",
          itemId: "fieldB",
          compName: "字段B",
          compType: "radio",
          business: "fieldB",
          group: "base",
          formData: {
            formName: "字段B",
            dependent: "fieldC",
            dependentValue: 1,
            candidateList: [{ id: 1, label: "选项1" }],
          },
          children: [],
        },
        {
          compId: "3",
          itemId: "fieldC",
          compName: "字段C",
          compType: "radio",
          business: "fieldC",
          group: "base",
          formData: {
            formName: "字段C",
            dependent: "fieldA",
            dependentValue: 1,
            candidateList: [{ id: 1, label: "选项1" }],
          },
          children: [],
        },
      ];

      const complexCircularManager = new DependencyManager(
        complexCircularFields
      );
      const validation = complexCircularManager.validateDependencyConfig();
      expect(validation.valid).toBe(false);
      expect(
        validation.errors.some((error) => error.includes("循环依赖"))
      ).toBe(true);
    });

    test("应该允许正常的依赖链", () => {
      const chainFields: ItemType[] = [
        {
          compId: "1",
          itemId: "fieldA",
          compName: "字段A",
          compType: "radio",
          business: "fieldA",
          group: "base",
          formData: {
            formName: "字段A",
            candidateList: [{ id: 1, label: "选项1" }],
          },
          children: [],
        },
        {
          compId: "2",
          itemId: "fieldB",
          compName: "字段B",
          compType: "radio",
          business: "fieldB",
          group: "base",
          formData: {
            formName: "字段B",
            dependent: "fieldA",
            dependentValue: 1,
            candidateList: [{ id: 1, label: "选项1" }],
          },
          children: [],
        },
        {
          compId: "3",
          itemId: "fieldC",
          compName: "字段C",
          compType: "input",
          business: "fieldC",
          group: "base",
          formData: {
            formName: "字段C",
            dependent: "fieldB",
            dependentValue: 1,
          },
          children: [],
        },
      ];

      const chainManager = new DependencyManager(chainFields);
      const validation = chainManager.validateDependencyConfig();
      expect(validation.valid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });
  });

  describe("边界情况和错误处理", () => {
    test("应该处理空的表单字段数组", () => {
      const emptyManager = new DependencyManager([]);
      expect(emptyManager.getVisibleFields()).toEqual([]);
      expect(emptyManager.getHiddenFields()).toEqual([]);
      expect(emptyManager.validateDependencyConfig().valid).toBe(true);
    });

    test("应该处理缺少itemId的字段", () => {
      const fieldsWithoutId: ItemType[] = [
        {
          compId: "1",
          compName: "无ID字段",
          compType: "input",
          business: "noId",
          group: "base",
          formData: {
            formName: "无ID字段",
          },
          children: [],
        },
      ];

      const managerWithoutId = new DependencyManager(fieldsWithoutId);
      expect(managerWithoutId.getVisibleFields()).toEqual([]);
      expect(managerWithoutId.getHiddenFields()).toEqual([]);
    });

    test("应该处理缺少formData的字段", () => {
      const fieldsWithoutFormData: ItemType[] = [
        {
          compId: "1",
          itemId: "field1",
          compName: "无FormData字段",
          compType: "input",
          business: "noFormData",
          group: "base",
          children: [],
        },
      ];

      const managerWithoutFormData = new DependencyManager(
        fieldsWithoutFormData
      );
      expect(managerWithoutFormData.shouldShowField("field1")).toBe(true);
      expect(managerWithoutFormData.getFieldDependency("field1")).toBeNull();
    });

    test("应该处理undefined和null值的比较", () => {
      manager.updateFieldValue("field1", undefined);
      expect(manager.shouldShowField("field2")).toBe(false);

      manager.updateFieldValue("field1", null);
      expect(manager.shouldShowField("field2")).toBe(false);
    });

    test("应该处理dependentValue为0的特殊情况", () => {
      const zeroValueFields: ItemType[] = [
        {
          compId: "1",
          itemId: "field1",
          compName: "单选字段",
          compType: "radio",
          business: "radio",
          group: "base",
          formData: {
            formName: "单选字段",
            candidateList: [
              { id: 0, label: "选项0" },
              { id: 1, label: "选项1" },
            ],
          },
          children: [],
        },
        {
          compId: "2",
          itemId: "field2",
          compName: "依赖字段",
          compType: "input",
          business: "dependent",
          group: "base",
          formData: {
            formName: "依赖字段",
            dependent: "field1",
            dependentValue: 0,
          },
          children: [],
        },
      ];

      const zeroValueManager = new DependencyManager(zeroValueFields);

      // 验证dependentValue为0时的验证逻辑
      const validation = zeroValueManager.validateDependencyConfig();
      expect(validation.valid).toBe(true);

      // 验证dependentValue为0时的显示逻辑
      zeroValueManager.updateFieldValue("field1", 0);
      expect(zeroValueManager.shouldShowField("field2")).toBe(true);
    });

    test("应该处理非单选字段的依赖验证", () => {
      const nonRadioFields: ItemType[] = [
        {
          compId: "1",
          itemId: "field1",
          compName: "输入字段",
          compType: "input",
          business: "input",
          group: "base",
          formData: {
            formName: "输入字段",
          },
          children: [],
        },
        {
          compId: "2",
          itemId: "field2",
          compName: "依赖字段",
          compType: "input",
          business: "dependent",
          group: "base",
          formData: {
            formName: "依赖字段",
            dependent: "field1",
            dependentValue: "test",
          },
          children: [],
        },
      ];

      const nonRadioManager = new DependencyManager(nonRadioFields);
      const validation = nonRadioManager.validateDependencyConfig();
      // 对于非单选字段，不进行候选值验证，应该通过
      expect(validation.valid).toBe(true);
    });

    test("应该处理candidateList为空或undefined的情况", () => {
      const emptyCandidateFields: ItemType[] = [
        {
          compId: "1",
          itemId: "field1",
          compName: "空候选列表字段",
          compType: "radio",
          business: "radio",
          group: "base",
          formData: {
            formName: "空候选列表字段",
            candidateList: [],
          },
          children: [],
        },
        {
          compId: "2",
          itemId: "field2",
          compName: "依赖字段",
          compType: "input",
          business: "dependent",
          group: "base",
          formData: {
            formName: "依赖字段",
            dependent: "field1",
            dependentValue: 1,
          },
          children: [],
        },
      ];

      const emptyCandidateManager = new DependencyManager(emptyCandidateFields);
      const validation = emptyCandidateManager.validateDependencyConfig();
      expect(validation.valid).toBe(false);
      expect(
        validation.errors.some((error) => error.includes("选项中不存在"))
      ).toBe(true);
    });
  });

  describe("性能和内存管理", () => {
    test("应该能够处理大量字段", () => {
      const largeFieldSet: ItemType[] = [];

      // 创建100个字段，其中50个有依赖关系
      for (let i = 0; i < 100; i++) {
        largeFieldSet.push({
          compId: i.toString(),
          itemId: `field${i}`,
          compName: `字段${i}`,
          compType: i % 2 === 0 ? "radio" : "input",
          business: `field${i}`,
          group: "base",
          formData: {
            formName: `字段${i}`,
            ...(i % 2 === 0
              ? {
                  candidateList: [
                    { id: 1, label: "选项1" },
                    { id: 2, label: "选项2" },
                  ],
                }
              : {}),
            ...(i > 0 && i % 2 === 1
              ? {
                  dependent: `field${i - 1}`,
                  dependentValue: 1,
                }
              : {}),
          },
          children: [],
        });
      }

      const largeManager = new DependencyManager(largeFieldSet);

      // 验证性能
      const startTime = performance.now();
      const validation = largeManager.validateDependencyConfig();
      const endTime = performance.now();

      expect(validation.valid).toBe(true);
      expect(endTime - startTime).toBeLessThan(100); // 应该在100ms内完成

      // 验证批量更新性能
      const updateStartTime = performance.now();
      const updates: Record<string, any> = {};
      for (let i = 0; i < 50; i++) {
        updates[`field${i * 2}`] = 1;
      }
      largeManager.batchUpdateFieldValues(updates);
      const updateEndTime = performance.now();

      expect(updateEndTime - updateStartTime).toBeLessThan(50); // 批量更新应该在50ms内完成
    });

    test("应该正确管理内存，避免内存泄漏", () => {
      const manager1 = new DependencyManager(mockFormFields);
      manager1.updateFieldValue("field1", 1);
      manager1.updateFieldValue("field2", "test");

      // 重置应该清空所有值
      manager1.resetFieldValues();
      expect(manager1.getAllFieldValues().size).toBe(0);

      // 创建新的管理器不应该受到之前实例的影响
      const manager2 = new DependencyManager(mockFormFields);
      expect(manager2.getAllFieldValues().size).toBe(0);
      expect(manager2.getVisibleFields()).toEqual(manager1.getVisibleFields());
    });
  });

  describe("实际业务场景测试", () => {
    test("应该正确处理MOC登记号依赖逻辑", () => {
      const mocFields: ItemType[] = [
        {
          compId: "1",
          itemId: "unitCategory",
          compName: "作业单位类别",
          compType: "radio",
          business: "unitCategory",
          group: "base",
          formData: {
            formName: "作业单位类别",
            candidateList: [
              { id: 1, label: "本厂" },
              { id: 2, label: "承包商" },
            ],
          },
          children: [],
        },
        {
          compId: "2",
          itemId: "mocNumber",
          compName: "MOC登记号",
          compType: "input",
          business: "mocNumber",
          group: "base",
          formData: {
            formName: "MOC登记号",
            dependent: "unitCategory",
            dependentValue: 2, // 只有选择"承包商"时才显示
          },
          children: [],
        },
      ];

      const mocManager = new DependencyManager(mocFields);

      // 初始状态：MOC登记号应该隐藏
      expect(mocManager.shouldShowField("mocNumber")).toBe(false);
      expect(mocManager.getHiddenFields()).toContain("mocNumber");

      // 选择"本厂"：MOC登记号仍然隐藏
      mocManager.updateFieldValue("unitCategory", 1);
      expect(mocManager.shouldShowField("mocNumber")).toBe(false);

      // 选择"承包商"：MOC登记号应该显示
      mocManager.updateFieldValue("unitCategory", 2);
      expect(mocManager.shouldShowField("mocNumber")).toBe(true);
      expect(mocManager.getVisibleFields()).toContain("mocNumber");
    });

    test("应该正确处理高风险作业方案登记号依赖逻辑", () => {
      const highRiskFields: ItemType[] = [
        {
          compId: "1",
          itemId: "riskLevel",
          compName: "风险等级",
          compType: "radio",
          business: "riskLevel",
          group: "base",
          formData: {
            formName: "风险等级",
            candidateList: [
              { id: 1, label: "低风险" },
              { id: 2, label: "中风险" },
              { id: 3, label: "高风险" },
            ],
          },
          children: [],
        },
        {
          compId: "2",
          itemId: "highRiskPlanNumber",
          compName: "高风险作业方案登记号",
          compType: "input",
          business: "highRiskPlanNumber",
          group: "base",
          formData: {
            formName: "高风险作业方案登记号",
            dependent: "riskLevel",
            dependentValue: 3, // 只有选择"高风险"时才显示
          },
          children: [],
        },
      ];

      const highRiskManager = new DependencyManager(highRiskFields);

      // 初始状态和低中风险：方案登记号隐藏
      expect(highRiskManager.shouldShowField("highRiskPlanNumber")).toBe(false);

      highRiskManager.updateFieldValue("riskLevel", 1);
      expect(highRiskManager.shouldShowField("highRiskPlanNumber")).toBe(false);

      highRiskManager.updateFieldValue("riskLevel", 2);
      expect(highRiskManager.shouldShowField("highRiskPlanNumber")).toBe(false);

      // 选择高风险：方案登记号显示
      highRiskManager.updateFieldValue("riskLevel", 3);
      expect(highRiskManager.shouldShowField("highRiskPlanNumber")).toBe(true);
    });

    test("应该正确处理多级依赖关系", () => {
      const multiLevelFields: ItemType[] = [
        {
          compId: "1",
          itemId: "workType",
          compName: "作业类型",
          compType: "radio",
          business: "workType",
          group: "base",
          formData: {
            formName: "作业类型",
            candidateList: [
              { id: 1, label: "普通作业" },
              { id: 2, label: "特殊作业" },
            ],
          },
          children: [],
        },
        {
          compId: "2",
          itemId: "specialWorkType",
          compName: "特殊作业类型",
          compType: "radio",
          business: "specialWorkType",
          group: "base",
          formData: {
            formName: "特殊作业类型",
            dependent: "workType",
            dependentValue: 2,
            candidateList: [
              { id: 1, label: "高处作业" },
              { id: 2, label: "受限空间作业" },
            ],
          },
          children: [],
        },
        {
          compId: "3",
          itemId: "safetyMeasures",
          compName: "安全措施",
          compType: "input",
          business: "safetyMeasures",
          group: "base",
          formData: {
            formName: "安全措施",
            dependent: "specialWorkType",
            dependentValue: 2, // 只有受限空间作业才需要特殊安全措施
          },
          children: [],
        },
      ];

      const multiLevelManager = new DependencyManager(multiLevelFields);

      // 初始状态：所有依赖字段都隐藏
      expect(multiLevelManager.shouldShowField("specialWorkType")).toBe(false);
      expect(multiLevelManager.shouldShowField("safetyMeasures")).toBe(false);

      // 选择普通作业：依赖字段仍然隐藏
      multiLevelManager.updateFieldValue("workType", 1);
      expect(multiLevelManager.shouldShowField("specialWorkType")).toBe(false);
      expect(multiLevelManager.shouldShowField("safetyMeasures")).toBe(false);

      // 选择特殊作业：第一级依赖字段显示
      multiLevelManager.updateFieldValue("workType", 2);
      expect(multiLevelManager.shouldShowField("specialWorkType")).toBe(true);
      expect(multiLevelManager.shouldShowField("safetyMeasures")).toBe(false);

      // 选择高处作业：第二级依赖字段仍然隐藏
      multiLevelManager.updateFieldValue("specialWorkType", 1);
      expect(multiLevelManager.shouldShowField("safetyMeasures")).toBe(false);

      // 选择受限空间作业：第二级依赖字段显示
      multiLevelManager.updateFieldValue("specialWorkType", 2);
      expect(multiLevelManager.shouldShowField("safetyMeasures")).toBe(true);

      // 验证完整的可见字段列表
      const visibleFields = multiLevelManager.getVisibleFields();
      expect(visibleFields).toContain("workType");
      expect(visibleFields).toContain("specialWorkType");
      expect(visibleFields).toContain("safetyMeasures");
    });
  });
});
