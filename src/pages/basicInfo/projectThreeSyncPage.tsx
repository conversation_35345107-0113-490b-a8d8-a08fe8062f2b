/* import { <PERSON><PERSON><PERSON><PERSON>, RoleContent, RoleSide } from './content'
import { RoleModal } from './modal'
 */

import {
  getAttachmentBasicInfoProjectThreeSyncList,
  getBasicInfoProjectThreeSync,
} from "api";
import {
  basicInfoProjectThreeSyncAtoms,
  basicInfoProjectThreeSyncDetailSideAtom,
} from "atoms";
import {
  PROJECTTHREESYNC_PROJECTTYPE_MAP,
  PROJECTTHREESYNC_STAGE_MAP,
  PROJECTTHREESYNC_STATUS_MAP,
} from "components";
import SideDetail from "components/detail/sideDetail";
import { useAtom } from "jotai";
import {
  BasicInfoProjectThreeSyncContent,
  BasicInfoProjectThreeSyncFilter,
} from "./content";
import {
  BasicInfoProjectThreeSyncModal,
  ProjectThreeSyncAcceptModal,
  ProjectThreeSyncApplyModal,
  ProjectThreeSyncAttachmentModal,
  ProjectThreeSyncBuildModal,
  ProjectThreeSyncDesignModal,
  ProjectThreeSyncTestProductionModal,
} from "./modal";

export function BasicInfoProjectThreeSyncPage({
  readonly = false,
  filter = {},
  ...restProps
}) {
  const [detailSideAtom, setDetailSideAtom] = useAtom(
    basicInfoProjectThreeSyncDetailSideAtom
  );
  const basicTab = {
    entity: basicInfoProjectThreeSyncAtoms.entity,
    entityTitle: "基本信息",
    api: getBasicInfoProjectThreeSync,
    infoOrList: 1,
    scheme: [
      {
        groupName: "基本信息",
        list: [
          { label: "ID", name: "id" },
          { label: "名称", name: "name" },
          { label: "编号", name: "code" },
          { label: "内容", name: "content" },
          { label: "位置", name: "location" },
          {
            label: "项目类型",
            name: "projectType",
            type: "enum",
            enumMap: PROJECTTHREESYNC_PROJECTTYPE_MAP,
          },
          { label: "所属装置", name: "plant", type: "entity" },
          {
            label: "状态",
            name: "status",
            type: "enum",
            enumMap: PROJECTTHREESYNC_STATUS_MAP,
          },
        ],
      },
      {
        groupName: "阶段信息",
        list: [
          { label: "申请开始时间", name: "applyBeginTime", type: "date" },
          { label: "申请结束时间", name: "applyEndTime", type: "date" },
          { label: "设计开始时间", name: "designBeginTime", type: "date" },
          { label: "设计结束时间", name: "designEndTime", type: "date" },
          { label: "施工开始时间", name: "buildBeginTime", type: "date" },
          { label: "施工结束时间", name: "buildEndTime", type: "date" },
          {
            label: "试生产开始时间",
            name: "testProductionBeginTime",
            type: "date",
          },
          {
            label: "试生产结束时间",
            name: "testProductionEndTime",
            type: "date",
          },
          { label: "验收开始时间", name: "acceptBeginTime", type: "date" },
          { label: "验收结束时间", name: "acceptEndTime", type: "date" },
        ],
      },
    ],
  };
  const attachmentTab = {
    entity: "BasicInfoProjectThreeSyncAttachment",
    entityTitle: "附件信息",
    api: getAttachmentBasicInfoProjectThreeSyncList,
    params: { id: detailSideAtom?.id, values: {} },
    infoOrList: 2,
    scheme: [
      {
        table: {
          dataSource: "results",
          columns: [
            { label: "ID", name: "id" },
            { label: "项目名称", name: "project", type: "entity" },
            {
              label: "阶段",
              name: "stage",
              type: "enum",
              enumMap: PROJECTTHREESYNC_STAGE_MAP,
            },
            { label: "文件说明", name: "content" },
            { label: "文件列表", name: "attachmentList", type: "file" },
            {
              label: "提交日期",
              name: "createdAt",
              type: "date",
            },
          ],
        },
      },
    ],
  };

  const tabPaneList = [basicTab, attachmentTab];

  return (
    <div className="flex flex-col gap-4 ">
      {/* <RoleSide />
      <RoleModal />
      <RoleFilter />
      <RoleContent /> */}
      <BasicInfoProjectThreeSyncFilter filter={filter} />
      <BasicInfoProjectThreeSyncModal />
      <ProjectThreeSyncApplyModal />
      <ProjectThreeSyncDesignModal />
      <ProjectThreeSyncBuildModal />
      <ProjectThreeSyncTestProductionModal />
      <ProjectThreeSyncAcceptModal />
      <ProjectThreeSyncAttachmentModal />
      <BasicInfoProjectThreeSyncContent
        readonly={readonly}
        filter={filter}
        {...restProps}
      />
      <SideDetail
        entityTitle="三同时详情"
        detailAtom={basicInfoProjectThreeSyncDetailSideAtom}
        tabPaneList={tabPaneList}
      />
    </div>
  );
}
