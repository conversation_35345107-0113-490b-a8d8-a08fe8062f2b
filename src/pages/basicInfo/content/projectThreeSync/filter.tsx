import { Form } from "@douyinfe/semi-ui";
import { useQuery } from "@tanstack/react-query";
import { getBasicInfoProjectThreeSyncList } from "api";
import { basicInfoProjectThreeSyncAtoms } from "atoms/basicInfo/projectThreeSync";
import {
  JOB_REPORT_STATUS_MAP,
  PROJECTTHREESYNC_PROJECTTYPE_MAP,
  PROJECTTHREESYNC_STATUS_MAP,
} from "components";
import { useFilterSearch } from "hooks";
import { useResetAtom } from "jotai/utils";
import { useEffect, useMemo } from "react";

export const BasicInfoProjectThreeSyncFilter = ({ filter }) => {
  const atoms = basicInfoProjectThreeSyncAtoms;
  const resetfilterAtom = useResetAtom(atoms.filter);
  const [handleSearch, handleReset] = useFilterSearch(atoms.filter);

  const entity = atoms.entity;
  const operation = "List";

  // reset filter when unmount
  useEffect(() => {
    return () => {
      resetfilterAtom();
    };
  }, []);

  const { data: list } = useQuery({
    queryKey: [`get${entity}${operation}`], //user-defined code here
    queryFn: () => getBasicInfoProjectThreeSyncList, //user-defined code here
  });
  const options = useMemo(() => {
    return list?.data?.results ?? [];
  }, [list]);

  return (
    <div className="flex flex-col bg-white shadow rounded relative ">
      <div className="text-gray-900 text-opacity-90 text-base font-semibold leading-snug px-4 py-3 border-b border-gray-900/10 ">
        筛选总览
      </div>

      <div className="p-4 pr-0">
        <Form
          initValues={filter}
          layout="horizontal"
          onSubmit={handleSearch}
          className="grid grid-cols-4 gap-y-4 "
        >
          {/* user-defined code here */}
          <Form.Select
            placeholder="项目类型"
            field="projectType"
            noLabel
            className="w-full"
          >
            {PROJECTTHREESYNC_PROJECTTYPE_MAP.map((item) => (
              <Form.Select.Option key={item.id} value={item.id}>
                {item.name}
              </Form.Select.Option>
            ))}
          </Form.Select>
          <Form.Select
            placeholder="项目状态"
            field="status"
            noLabel
            className="w-full"
          >
            {PROJECTTHREESYNC_STATUS_MAP.map((item) => (
              <Form.Select.Option key={item.id} value={item.id}>
                {item.name}
              </Form.Select.Option>
            ))}
          </Form.Select>
          <Form.Select
            placeholder="上报状态"
            field="reportStatus"
            noLabel
            className="w-full"
          >
            {JOB_REPORT_STATUS_MAP.map((item) => (
              <Form.Select.Option key={item.id} value={item.id}>
                {item.name}
              </Form.Select.Option>
            ))}
          </Form.Select>

          {/* initValue={filter?.type}
          disabled */}
          <div className="flex gap-2">
            <button className="btn rounded btn-primary btn-sm">
              查&nbsp;&nbsp;询
            </button>
            <button
              className="btn btn-sm rounded"
              type="reset"
              onClick={handleReset}
            >
              重&nbsp;&nbsp;置
            </button>
          </div>
        </Form>
      </div>
    </div>
  );
};
