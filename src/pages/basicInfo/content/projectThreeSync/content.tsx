import { useQueryClient } from "@tanstack/react-query";
import { basicInfoProjectThreeSyncApis } from "api";
import {
  basicInfoProjectThreeSyncAcceptModalAtom,
  basicInfoProjectThreeSyncApplyModalAtom,
  basicInfoProjectThreeSyncAtoms,
  basicInfoProjectThreeSyncAttachmentModalAtom,
  basicInfoProjectThreeSyncBuildModalAtom,
  basicInfoProjectThreeSyncDesignModalAtom,
  basicInfoProjectThreeSyncDetailSideAtom,
  basicInfoProjectThreeSyncTestProductionModalAtom,
} from "atoms";
import { ExportProvider, List } from "components";
import { useAtom } from "jotai";
import { FC, useCallback } from "react";

type BasicInfoProjectThreeSyncContentProps = {
  callback?: any;
  layout?: string;
  referAtom?: any;
  readonly?: boolean;
  filter?: any;
};
export const BasicInfoProjectThreeSyncContent: FC<
  BasicInfoProjectThreeSyncContentProps
> = ({
  callback,
  layout,
  referAtom,
  readonly = false,
  filter = {},
  ...restProps
}) => {
  if (layout === "modal") {
    readonly = true;
  }
  const queryClient = useQueryClient();
  const queryKey = "list" + basicInfoProjectThreeSyncAtoms.entity;

  const [detailSideAtom, setDetailSideAtom] = useAtom(
    basicInfoProjectThreeSyncDetailSideAtom
  );
  const handleOpenDetailSide = useCallback(
    (record) => {
      setDetailSideAtom({
        id: record.id,
        show: true,
      });
    },
    [setDetailSideAtom]
  );

  const detailOp = {
    engName: "detail",
    chnName: "查看详情",
    func: handleOpenDetailSide,
  };

  const [applyModalAtom, setApplyModalAtom] = useAtom(
    basicInfoProjectThreeSyncApplyModalAtom
  );
  const handleOpenApplyModal = useCallback(
    (record: any) => {
      setApplyModalAtom({
        id: record?.id,
        show: true,
        record: record,
      });
    },
    [setApplyModalAtom]
  );
  const toggleApply = (record: any) => {
    return record.status === 1
      ? {
          engName: "apply",
          chnName: "立项",
          func: handleOpenApplyModal,
        }
      : null;
  };

  const [designModalAtom, setDesignModalAtom] = useAtom(
    basicInfoProjectThreeSyncDesignModalAtom
  );
  const handleOpenDesignModal = useCallback(
    (record: any) => {
      setDesignModalAtom({
        id: record?.id,
        show: true,
        record: record,
      });
    },
    [setDesignModalAtom]
  );
  const toggleDesign = (record: any) => {
    return record.status === 2
      ? {
          engName: "design",
          chnName: "设计",
          func: handleOpenDesignModal,
        }
      : null;
  };

  const [buildModalAtom, setBuildModalAtom] = useAtom(
    basicInfoProjectThreeSyncBuildModalAtom
  );
  const handleOpenBuildModal = useCallback(
    (record: any) => {
      setBuildModalAtom({
        id: record?.id,
        show: true,
        record: record,
      });
    },
    [setBuildModalAtom]
  );
  const toggleBuild = (record: any) => {
    return record.status === 3
      ? {
          engName: "build",
          chnName: "施工",
          func: handleOpenBuildModal,
        }
      : null;
  };

  const [testProductionModalAtom, setTestProductionModalAtom] = useAtom(
    basicInfoProjectThreeSyncTestProductionModalAtom
  );
  const handleOpenTestProductionModal = useCallback(
    (record: any) => {
      setTestProductionModalAtom({
        id: record?.id,
        show: true,
        record: record,
      });
    },
    [setTestProductionModalAtom]
  );
  const toggleTestProduction = (record: any) => {
    return record.status === 4
      ? {
          engName: "testProduction",
          chnName: "试运行",
          func: handleOpenTestProductionModal,
        }
      : null;
  };

  const [acceptModalAtom, setAcceptModalAtom] = useAtom(
    basicInfoProjectThreeSyncAcceptModalAtom
  );
  const handleOpenAcceptModal = useCallback(
    (record: any) => {
      setAcceptModalAtom({
        id: record?.id,
        show: true,
        record: record,
      });
    },
    [setAcceptModalAtom]
  );
  const toggleAccept = (record: any) => {
    return record.status === 5
      ? {
          engName: "accept",
          chnName: "验收",
          func: handleOpenAcceptModal,
        }
      : null;
  };

  const [attachmentModalAtom, setAttachmentModalAtom] = useAtom(
    basicInfoProjectThreeSyncAttachmentModalAtom
  );
  const handleOpenAttachmentModal = useCallback(
    (record: any) => {
      setAttachmentModalAtom({
        id: record?.id,
        show: true,
        record: record,
      });
    },
    [setAttachmentModalAtom]
  );
  const attachmentOp = {
    engName: "attachment",
    chnName: "附件",
    func: handleOpenAttachmentModal,
  };

  return (
    <ExportProvider>
      <List
        atoms={basicInfoProjectThreeSyncAtoms}
        apis={basicInfoProjectThreeSyncApis}
        // operations, dynamicOperationFuncs
        filter={filter}
        callback={callback}
        referAtom={referAtom}
        layout={layout}
        readonly={readonly}
        _operations={[detailOp, attachmentOp]}
        dynamicOperationFuncs={[
          toggleApply,
          toggleDesign,
          toggleBuild,
          toggleTestProduction,
          toggleAccept,
        ]}
        {...restProps}
      />
    </ExportProvider>
  );
};
