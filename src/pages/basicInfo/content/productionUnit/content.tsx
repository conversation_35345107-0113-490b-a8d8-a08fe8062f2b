import { productionUnitApis } from "api";
import { productionUnitAtoms, productionUnitDetailSideAtom } from "atoms";
import {
  productionUnitMaintenanceFinishModalAtom,
  productionUnitMaintenanceModalAtom,
  productionUnitStartFinishModalAtom,
  productionUnitStartModalAtom,
  productionUnitStopFinishModalAtom,
  productionUnitStopModalAtom,
} from "atoms/basicInfo/productionUnit";
import { List } from "components";
import { useAtom } from "jotai";
import { useCallback } from "react";

export const ProductionUnitContent = () => {
  const [detailSide, setDetailSide] = useAtom(productionUnitDetailSideAtom);
  const handleOpenDetailSide = useCallback(
    (record) => {
      setDetailSide({
        id: record.id,
        show: true,
      });
    },
    [setDetailSide]
  );
  const detailOp = {
    engName: "detail",
    chnName: "查看详情",
    func: handleOpenDetailSide,
  };

  const importProps = {
    entity: "生产装置",
    excelType: "production_unit_template",
    downUrl: encodeURI("/static/template/生产装置信息导入模板.xlsx"),
    tip: "请先维护好重大危险源和危险工艺信息，否则导入会失败",
  };

  const [maintenanceModal, setMaintenanceModal] = useAtom(
    productionUnitMaintenanceModalAtom
  );
  const handleOpenMaintenanceModal = useCallback(
    (record) => {
      setMaintenanceModal({
        id: record.id,
        record: record,
        show: true,
      });
    },
    [setMaintenanceModal]
  );
  const toggleMaintenance = (record) => {
    return record?.status === 1
      ? {
          engName: "maintenance",
          chnName: "检修",
          func: handleOpenMaintenanceModal,
        }
      : null;
  };

  const [maintenanceFinishModal, setMaintenanceFinishModal] = useAtom(
    productionUnitMaintenanceFinishModalAtom
  );
  const handleOpenMaintenanceFinishModal = useCallback(
    (record) => {
      setMaintenanceFinishModal({
        id: record.id,
        record: record,
        show: true,
      });
    },
    [setMaintenanceFinishModal]
  );
  const toggleMaintenanceFinish = (record) => {
    return record?.status === 5
      ? {
          engName: "maintenanceFinish",
          chnName: "检修结束",
          func: handleOpenMaintenanceFinishModal,
        }
      : null;
  };

  const [startModal, setStartModal] = useAtom(productionUnitStartModalAtom);
  const handleOpenStartModal = useCallback(
    (record) => {
      setStartModal({
        id: record.id,
        record: record,
        show: true,
      });
    },
    [setStartModal]
  );
  const toggleStart = (record) => {
    return record?.status === 1
      ? {
          engName: "start",
          chnName: "开车",
          func: handleOpenStartModal,
        }
      : null;
  };

  const [startFinishModal, setStartFinishModal] = useAtom(
    productionUnitStartFinishModalAtom
  );
  const handleOpenStartFinishModal = useCallback(
    (record) => {
      setStartFinishModal({
        id: record.id,
        record: record,
        show: true,
      });
    },
    [setStartFinishModal]
  );
  const toggleStartFinish = (record) => {
    return record?.status === 2
      ? {
          engName: "startFinish",
          chnName: "开车结束",
          func: handleOpenStartFinishModal,
        }
      : null;
  };

  const [stopModal, setStopModal] = useAtom(productionUnitStopModalAtom);
  const handleOpenStopModal = useCallback(
    (record) => {
      setStopModal({
        id: record.id,
        record: record,
        show: true,
      });
    },
    [setStopModal]
  );
  const toggleStop = (record) => {
    return record?.status === 3
      ? {
          engName: "stop",
          chnName: "停车",
          func: handleOpenStopModal,
        }
      : null;
  };

  const [stopFinishModal, setStopFinishModal] = useAtom(
    productionUnitStopFinishModalAtom
  );
  const handleOpenStopFinishModal = useCallback(
    (record) => {
      setStopFinishModal({
        id: record.id,
        record: record,
        show: true,
      });
    },
    [setStopFinishModal]
  );
  const toggleStopFinish = (record) => {
    return record?.status === 4
      ? {
          engName: "stopFinish",
          chnName: "停车结束",
          func: handleOpenStopFinishModal,
        }
      : null;
  };

  return (
    <List
      atoms={productionUnitAtoms}
      apis={productionUnitApis}
      importProps={importProps}
      _operations={[detailOp]}
      dynamicOperationFuncs={[
        toggleMaintenance,
        toggleMaintenanceFinish,
        toggleStart,
        toggleStartFinish,
        toggleStop,
        toggleStopFinish,
      ]}
    />
  );
};
