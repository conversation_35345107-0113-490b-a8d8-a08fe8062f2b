import {
  Col,
  Form,
  Modal,
  Row,
  TextArea,
  Toast,
  useFormState,
} from "@douyinfe/semi-ui";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { applyBasicInfoProjectThreeSync, testProductionBasicInfoProjectThreeSync } from "api";
import {
  basicInfoProjectThreeSyncApplyModalAtom,
  basicInfoProjectThreeSyncAtoms,
  basicInfoProjectThreeSyncTestProductionModalAtom,
} from "atoms";
import { PROJECTTHREESYNC_STAGERESULT_MAP } from "components";
import { Draft, DraftTrigger, destroyDraft } from "components/Draft";
import { useAtom } from "jotai";
import { useCallback, useRef } from "react";

const FormDebugComponentUsingFormState = () => {
  const formState = useFormState();
  return (
    <>
      <TextArea rows={8} value={JSON.stringify(formState.values, null, 2)} />
    </>
  );
};

export const ProjectThreeSyncTestProductionModal = () => {
  const operation = "TestProduction";
  const entityCname = "三同时试运行";
  const requiredRule = { required: true, message: "此为必填项" };
  const gutter = 24;

  const atoms = basicInfoProjectThreeSyncAtoms;

  const entity = atoms.entity;
  const uniqueKey = `${entity}${operation}`;

  const [editModalAtom, setEditModalAtom] = useAtom(
    basicInfoProjectThreeSyncTestProductionModalAtom
  );
  const [fnAtom] = useAtom(atoms.Fn);

  const title = editModalAtom?.record?.name + " " + entityCname;
  const rules = [requiredRule];

  const queryClient = useQueryClient();
  const queryKey = "list" + entity;

  const getFormApiRef = useRef<any>(null);

  const handleSetFormApi = useCallback(
    (formApi) => {
      getFormApiRef.current = formApi;
    },
    [getFormApiRef]
  );

  const mutation = useMutation({
    mutationFn: testProductionBasicInfoProjectThreeSync,
    onSuccess: async (res) => {
      if (res?.code === 0) {
        let opts = {
          content: `操作成功!`,
          duration: 2,
        };
        Toast.success(opts);
        queryClient.invalidateQueries({ queryKey: [queryKey] });
        fnAtom?.refetch?.();
        // getFormApiRef.current?.reset?.();
        getFormApiRef.current?.setValues?.({}, { isOverride: true });
        destroyDraft(uniqueKey);
        setEditModalAtom({
          id: "",
          show: false,
          record: {},
        });
      }
    },
  });

  const handleClose = useCallback(() => {
    if (mutation.isLoading) {
      return;
    }
    destroyDraft(`${uniqueKey}`);
    setEditModalAtom({
      id: "",
      show: false,
      record: {},
    });
  }, [setEditModalAtom, mutation]);

  const validateEndTime = (values: any) => {
    if (
      values.testProductionBeginTime &&
      values.testProductionEndTime &&
      new Date(values.testProductionEndTime) <= new Date(values.testProductionBeginTime)
    ) {
      return { testProductionEndTime: "试运行结束时间必须晚于开始时间" };
    }
    return {};
  };

  const handleOk = () => {
    console.log("handleOk");
    if (mutation.isLoading) {
      return;
    }

    console.log(editModalAtom);
    getFormApiRef.current
      .validate()
      .then((values: any) => {
        // 手动验证结束时间
        const endTimeError = validateEndTime(values);
        if (Object.keys(endTimeError).length > 0) {
          getFormApiRef.current.setError(
            "testProductionEndTime",
            endTimeError.testProductionEndTime
          );
          return;
        }

        let obj = {
          ...values,
          //user-defined code here
        };
        console.log(obj);
        if (editModalAtom?.id) {
          mutation.mutate({
            id: editModalAtom?.id,
            values: obj,
          });
        } else {
          mutation.mutate(obj);
        }
      })
      .catch((errors) => {
        console.log("Form validation errors:", errors);
        const [formFieldId] = Object.keys(errors || {});
        if (formFieldId) {
          document.getElementById(formFieldId)?.scrollIntoViewIfNeeded?.();
        }
      });
  };

  return (
    <>
      <DraftTrigger id={uniqueKey} draftAtom={atoms.editModal} />
      <Modal
        title={title}
        visible={editModalAtom?.show ?? false}
        onCancel={handleClose}
        maskClosable={false}
        keepDOM
        width={800}
        footer={
          <div className="flex gap-2 justify-end">
            <button className="btn rounded btn-sm" onClick={handleClose}>
              取消
            </button>
            {mutation.isLoading ? (
              <button className="btn rounded btn-primary btn-sm btn-disabled">
                <span className="loading loading-spinner loading-xs"></span>
                确定
              </button>
            ) : (
              <button
                className="btn rounded btn-primary btn-sm"
                onClick={handleOk}
              >
                确定
              </button>
            )}
          </div>
        }
        centered
      >
        <Form
          labelPosition="left"
          labelAlign="right"
          labelWidth={130}
          getFormApi={handleSetFormApi}
        >
          {({ formState }) => (
            <>
              {/* <FormDebugComponentUsingFormState /> */}

              {/* add form items here */}
              <Row gutter={gutter}>
                <Col span={12}>
                  <Form.DatePicker
                    label="试运行开始时间"
                    field="testProductionBeginTime"
                    className="w-full"
                    rules={rules}
                  />
                </Col>
                <Col span={12}>
                  <Form.DatePicker
                    label="试运行结束时间"
                    field="testProductionEndTime"
                    className="w-full"
                    rules={rules}
                  />
                </Col>
                <Col span={12}>
                  <Form.Select
                    label="阶段结果"
                    field="stageResult"
                    className="w-full"
                    rules={rules}
                  >
                    {PROJECTTHREESYNC_STAGERESULT_MAP.map((item) => (
                      <Form.Select.Option key={item.id} value={item.id}>
                        {item.name}
                      </Form.Select.Option>
                    ))}
                  </Form.Select>
                </Col>
              </Row>
              {editModalAtom?.id ? null : (
                <Draft id={uniqueKey} draftAtom={atoms.editModal} />
              )}
            </>
          )}
        </Form>
      </Modal>
    </>
  );
};
