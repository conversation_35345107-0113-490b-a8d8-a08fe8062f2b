import {
  Col,
  Form,
  Modal,
  Row,
  TextArea,
  Toast,
  useFormState,
} from "@douyinfe/semi-ui";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { attachmentBasicInfoProjectThreeSync } from "api";
import {
  basicInfoProjectThreeSyncAtoms,
  basicInfoProjectThreeSyncAttachmentModalAtom,
} from "atoms";
import { PROJECTTHREESYNC_STAGE_MAP, Upload } from "components";
import { destroyDraft, Draft, DraftTrigger } from "components/Draft";
import { useAtom } from "jotai";
import { useCallback, useRef } from "react";

const FormDebugComponentUsingFormState = () => {
  const formState = useFormState();
  return (
    <>
      <TextArea rows={8} value={JSON.stringify(formState.values, null, 2)} />
    </>
  );
};

export const ProjectThreeSyncAttachmentModal = () => {
  const operation = "Attachment";
  const entityCname = "三同时添加附件";
  const requiredRule = { required: true, message: "此为必填项" };
  const gutter = 24;

  const atoms = basicInfoProjectThreeSyncAtoms;

  const entity = atoms.entity;
  const uniqueKey = `${entity}${operation}`;

  const [editModalAtom, setEditModalAtom] = useAtom(
    basicInfoProjectThreeSyncAttachmentModalAtom
  );
  const [fnAtom] = useAtom(atoms.Fn);

  const title = editModalAtom?.record?.name + " " + entityCname;
  const rules = [requiredRule];

  const queryClient = useQueryClient();
  const queryKey = "list" + entity;

  const getFormApiRef = useRef<any>(null);

  const handleSetFormApi = useCallback(
    (formApi) => {
      getFormApiRef.current = formApi;
    },
    [getFormApiRef]
  );

  const mutation = useMutation({
    mutationFn: attachmentBasicInfoProjectThreeSync,
    onSuccess: async (res) => {
      if (res?.code === 0) {
        let opts = {
          content: `操作成功!`,
          duration: 2,
        };
        Toast.success(opts);
        queryClient.invalidateQueries({ queryKey: [queryKey] });
        fnAtom?.refetch?.();
        // getFormApiRef.current?.reset?.();
        getFormApiRef.current?.setValues?.({}, { isOverride: true });
        destroyDraft(uniqueKey);
        setEditModalAtom({
          id: "",
          show: false,
          record: {},
        });
      }
    },
  });

  const handleClose = useCallback(() => {
    if (mutation.isLoading) {
      return;
    }
    destroyDraft(`${uniqueKey}`);
    setEditModalAtom({
      id: "",
      show: false,
      record: {},
    });
  }, [setEditModalAtom, mutation]);

  const validateEndTime = (values: any) => {
    if (
      values.applyBeginTime &&
      values.applyEndTime &&
      new Date(values.applyEndTime) <= new Date(values.applyBeginTime)
    ) {
      return { applyEndTime: "申请立项结束时间必须晚于开始时间" };
    }
    return {};
  };

  const handleOk = () => {
    console.log("handleOk");
    if (mutation.isLoading) {
      return;
    }

    console.log(editModalAtom);
    getFormApiRef.current
      .validate()
      .then((values: any) => {
        // 手动验证结束时间
        const endTimeError = validateEndTime(values);
        if (Object.keys(endTimeError).length > 0) {
          getFormApiRef.current.setError(
            "applyEndTime",
            endTimeError.applyEndTime
          );
          return;
        }

        let obj = {
          ...values,
          //user-defined code here
        };
        console.log(obj);
        if (editModalAtom?.id) {
          mutation.mutate({
            id: editModalAtom?.id,
            values: obj,
          });
        } else {
          mutation.mutate(obj);
        }
      })
      .catch((errors) => {
        console.log("Form validation errors:", errors);
        const [formFieldId] = Object.keys(errors || {});
        if (formFieldId) {
          document.getElementById(formFieldId)?.scrollIntoViewIfNeeded?.();
        }
      });
  };

  return (
    <>
      <DraftTrigger id={uniqueKey} draftAtom={atoms.editModal} />
      <Modal
        title={title}
        visible={editModalAtom?.show ?? false}
        onCancel={handleClose}
        maskClosable={false}
        keepDOM
        width={800}
        footer={
          <div className="flex gap-2 justify-end">
            <button className="btn rounded btn-sm" onClick={handleClose}>
              取消
            </button>
            {mutation.isLoading ? (
              <button className="btn rounded btn-primary btn-sm btn-disabled">
                <span className="loading loading-spinner loading-xs"></span>
                确定
              </button>
            ) : (
              <button
                className="btn rounded btn-primary btn-sm"
                onClick={handleOk}
              >
                确定
              </button>
            )}
          </div>
        }
        centered
      >
        <Form
          labelPosition="left"
          labelAlign="right"
          labelWidth={130}
          getFormApi={handleSetFormApi}
        >
          {({ formState }) => (
            <>
              {/* <FormDebugComponentUsingFormState /> */}

              {/* add form items here */}
              <Row gutter={gutter}>
                <Col span={12}>
                  <Form.Select
                    label="项目阶段"
                    field="stage"
                    className="w-full"
                    rules={rules}
                  >
                    {PROJECTTHREESYNC_STAGE_MAP.map((item) => (
                      <Form.Select.Option key={item.id} value={item.id}>
                        {item.name}
                      </Form.Select.Option>
                    ))}
                  </Form.Select>
                </Col>
                </Row>
                <Row gutter={gutter}>
                <Col span={12}>
                  <Form.TextArea
                    label="文件说明"
                    field="content"
                    autosize
                    trigger="blur"
                  />
                </Col>
                </Row>
                <Row gutter={gutter}>
                <Col span={24}>
                  {/* 图像(.jpg，.png，.gif，.jpeg)，文档(.doc，.docx，.pdf，.xlsx，.xls，.ppt)   大小限制在50M */}
                  <Upload
                    label="上传文件"
                    formField="attachmentList"
                    field="attachmentList_upload"
                    arrayProcessType="array"
                    type="file"
                    listType="list"
                    accept=".jpg,.png,.gif,.jpeg,.txt,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.pdf,.flv,.rmvb,.mkv,.mp4,.mvb,.wma,.mp3"
                    maxSize={51200} //KB
                  />
                </Col>
              </Row>
              {editModalAtom?.id ? null : (
                <Draft id={uniqueKey} draftAtom={atoms.editModal} />
              )}
            </>
          )}
        </Form>
      </Modal>
    </>
  );
};
