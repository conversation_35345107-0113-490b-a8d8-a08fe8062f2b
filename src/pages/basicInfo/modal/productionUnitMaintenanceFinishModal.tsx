import {
  Col,
  Form,
  Modal,
  Row,
  TextArea,
  Toast,
  useFormState,
} from "@douyinfe/semi-ui";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { finishMaintenanceProductionUnit, productionUnitApis } from "api";
import {
  productionUnitAtoms,
  productionUnitMaintenanceFinishModalAtom,
} from "atoms";
import { Draft, DraftTrigger, destroyDraft } from "components/Draft";
import { useAtom } from "jotai";
import { useCallback, useRef } from "react";

const FormDebugComponentUsingFormState = () => {
  const formState = useFormState();
  return (
    <>
      <TextArea rows={8} value={JSON.stringify(formState.values, null, 2)} />
    </>
  );
};

export const ProductionUnitMaintenanceFinishModal = () => {
  const operation = "Edit";
  const entityCname = "生产装置检修";
  const newTitle = "结束" + entityCname; //user-defined code here
  const editTitle = "结束" + entityCname; //user-defined code here
  const requiredRule = { required: true, message: "此为必填项" };
  const gutter = 24;

  const atoms = productionUnitAtoms;
  const apis = productionUnitApis;

  const entity = atoms.entity + "MaintenanceFinish";
  const uniqueKey = `${entity}${operation}`;

  const [editModalAtom, setEditModalAtom] = useAtom(
    productionUnitMaintenanceFinishModalAtom
  );
  const [fnAtom] = useAtom(atoms.Fn);

  const title = editModalAtom?.id ? editTitle : newTitle;
  const rules = [requiredRule];

  const queryClient = useQueryClient();
  const queryKey = "list" + entity;

  const getFormApiRef = useRef<any>(null);

  const handleSetFormApi = useCallback(
    (formApi) => {
      getFormApiRef.current = formApi;
    },
    [getFormApiRef]
  );

  /* const { isLoading, data } = useQuery({
    queryKey: [`${entity}-${editModalAtom?.id ?? ""}`],
    queryFn: () => {
      if (editModalAtom?.id) {
        return apis.get(editModalAtom?.id);
      }
    },
    enabled: !!editModalAtom?.id,
  });
  // 自动填充表单
  useEffect(() => {
    if (editModalAtom?.id && data?.data?.id && getFormApiRef.current) {
      const items = omit([], data?.data);
      getFormApiRef.current.setValues(
        {
          ...filterEditData(items),
          //user-defined code here
        },
        { isOverride: true }
      );
    } else {
      getFormApiRef?.current?.reset?.();
      getFormApiRef.current?.setValues?.(
        { equipmentId: editModalAtom?.equipment?.id },
        { isOverride: true }
      );
    }
  }, [editModalAtom, data, getFormApiRef]); */

  const mutation = useMutation({
    mutationFn: finishMaintenanceProductionUnit,
    onSuccess: async (res) => {
      if (res?.code === 0) {
        let opts = {
          content: `操作成功!`,
          duration: 2,
        };
        Toast.success(opts);
        queryClient.invalidateQueries({ queryKey: [queryKey] });
        fnAtom?.refetch?.();
        // getFormApiRef.current?.reset?.();
        getFormApiRef.current?.setValues?.({}, { isOverride: true });
        destroyDraft(uniqueKey);
        setEditModalAtom({
          id: "",
          show: false,
          record: {},
        });
      }
    },
  });

  const handleClose = useCallback(() => {
    if (mutation.isLoading) {
      return;
    }
    destroyDraft(`${uniqueKey}`);
    setEditModalAtom({
      id: "",
      show: false,
      record: {},
    });
  }, [setEditModalAtom, mutation]);

  const validateEndTime = (values: any) => {
    if (
      values.startTime &&
      values.endTime &&
      new Date(values.endTime) <= new Date(values.startTime)
    ) {
      return { endTime: "结束时间必须晚于开始时间" };
    }
    return {};
  };

  const handleOk = () => {
    if (mutation.isLoading) {
      return;
    }

    getFormApiRef.current
      .validate()
      .then((values: any) => {
        // 手动验证结束时间
        const endTimeError = validateEndTime(values);
        if (Object.keys(endTimeError).length > 0) {
          getFormApiRef.current.setError("endTime", endTimeError.endTime);
          return;
        }

        let obj = {
          ...values,
          //user-defined code here
        };

        if (editModalAtom?.id) {
          mutation.mutate({
            id: editModalAtom?.id,
            values: obj,
          });
        } else {
          mutation.mutate(obj);
        }
      })
      .catch((errors) => {
        console.log("Form validation errors:", errors);
        const [formFieldId] = Object.keys(errors || {});
        if (formFieldId) {
          document.getElementById(formFieldId)?.scrollIntoViewIfNeeded?.();
        }
      });
  };

  return (
    <>
      <DraftTrigger id={uniqueKey} draftAtom={atoms.editModal} />
      <Modal
        title={title}
        visible={editModalAtom?.show ?? false}
        onCancel={handleClose}
        maskClosable={false}
        keepDOM
        width={800}
        footer={
          <div className="flex gap-2 justify-end">
            <button className="btn rounded btn-sm" onClick={handleClose}>
              取消
            </button>
            {mutation.isLoading ? (
              <button className="btn rounded btn-primary btn-sm btn-disabled">
                <span className="loading loading-spinner loading-xs"></span>
                确定
              </button>
            ) : (
              <button
                className="btn rounded btn-primary btn-sm"
                onClick={handleOk}
              >
                确定
              </button>
            )}
          </div>
        }
        centered
      >
        <Form
          labelPosition="left"
          labelAlign="right"
          labelWidth={130}
          getFormApi={handleSetFormApi}
        >
          {({ formState }) => (
            <>
              {/* <FormDebugComponentUsingFormState /> */}

              {/* add form items here */}
              <Row gutter={gutter}>
                <Col span={12}>
                  <Form.DatePicker
                    label="结束时间"
                    type="dateTime"
                    field="endTime"
                    className="w-full"
                    rules={rules}
                    disabledDate={(date) => date >= new Date()}
                  />
                </Col>
              </Row>
              <Row gutter={gutter}>
                <Col span={12}>
                  <Form.TextArea
                    label="备注"
                    field="note"
                    autosize
                    trigger="blur"
                  />
                </Col>
              </Row>
              {editModalAtom?.id ? null : (
                <Draft id={uniqueKey} draftAtom={atoms.editModal} />
              )}
            </>
          )}
        </Form>
      </Modal>
    </>
  );
};
