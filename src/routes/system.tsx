// 系统管理相关路由
// 包含 SystemMap 和 HiddenMenu

import {
  AboutPage,
  ActionRecordPage,
  CalendarPage,
  DicPage,
  LoginRecordPage,
  PermissionPage,
} from "pages/basicInfo";
import { DisplayCategoryPage } from "pages/majorHazard";
import {
  AlarmPushConfigPage,
  NoticePushConfigPage,
  OmConfigPage,
  SystemExternalLinkPage,
} from "pages/system";
import HiddenAuthPage from "pages/system/hiddenAuthPage";
import HiddenBasicInfoSettingsPage from "pages/system/hiddenBasicInfoSettingsPage";
import HiddenDGSettingsPage from "pages/system/hiddenDGSettingsPage";
import HiddenEquipmentSettingsPage from "pages/system/hiddenEquipmentSettingsPage";
import HiddenSensorSettingsPage from "pages/system/hiddenSensorSettingsPage";
import HiddenSettingsPage from "pages/system/hiddenSettingsPage";
import HiddenSWSettingsPage from "pages/system/hiddenSWSettingsPage";
import {
  FormConfigPage,
  JobCategoryPage,
  JsTemplatePage,
  PreviewPage,
  PrintConfigPage,
} from "pages/ticket";
import {
  HiddenRoutes,
  SpecialWorkRoutes,
  SystemSettingsRoutes,
} from "utils/routerConstants";
import type { ChildrenMap } from "./types";
import { generateLoader } from "./utils";

export const SystemMap: ChildrenMap[] = generateLoader(
  [
    {
      path: SystemSettingsRoutes.OM_CONFIG, // "/om_config",
      element: <OmConfigPage />,
      name: "后台配置",
    },
    {
      path: SystemSettingsRoutes.BIGSCREEN_LEGEND, // "/bigscreen_legend",
      element: <DisplayCategoryPage />,
      name: "大屏图例管理",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
      ],
    },
    {
      path: SystemSettingsRoutes.DIC, // "/dic",
      element: <DicPage />,
      name: "字典管理",
      meta: [
        {
          action: "editDicValue",
          name: "字典值",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
      ],
    },
    {
      path: SystemSettingsRoutes.NOTICE_PUSH_CONFIG, // "/notice_push_config",
      element: <NoticePushConfigPage />,
      name: "提醒推送配置",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
    {
      path: SystemSettingsRoutes.ALARM_PUSH_CONFIG, // "/alarm_push_config",
      element: <AlarmPushConfigPage />,
      name: "报警规则配置",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
    {
      path: SystemSettingsRoutes.EXTERNAL_LINK, // "/external_link",
      element: <SystemExternalLinkPage />,
      name: "外部链接管理",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
    {
      path: SystemSettingsRoutes.LOGINRECORDS, // "/loginRecords",
      element: <LoginRecordPage />,
      name: "登录记录",
    },
    {
      path: SystemSettingsRoutes.ACTIONRECORDS, // "/actionRecords",
      element: <ActionRecordPage />,
      name: "操作记录",
    },
    {
      path: SystemSettingsRoutes.CALENDAR, // "/Calendar",
      element: <CalendarPage />,
      name: "工作日历",
      meta: [
        {
          action: "edit",
          name: "编辑",
        },
      ],
    },
    {
      path: SystemSettingsRoutes.ABOUT, // "/about",
      element: <AboutPage />,
      name: "关于系统",
    },
  ],
  1000
);

export const HiddenMenu: ChildrenMap[] = [
  {
    path: HiddenRoutes.SETTINGS, // "/hidden/settings",
    element: <HiddenSettingsPage />,
    name: "系统隐藏设置",
  },
  {
    path: HiddenRoutes.AUTH,
    element: <HiddenAuthPage />,
    name: "认证方式配置",
  },
  {
    path: HiddenRoutes.PERMISSION, // "/hidden/permission",
    element: <PermissionPage />,
    name: "权限配置",
  },
  {
    path: HiddenRoutes.JOB_CATEGORY, // "/hidden/job_category",
    element: <JobCategoryPage />,
    name: "作业类型配置",
  },
  {
    path: HiddenRoutes.DoubleGuard, // "/hidden/double_guard",
    element: <HiddenDGSettingsPage />,
    name: "双预防上报设置",
  },
  {
    path: HiddenRoutes.SpecialWork, // "/hidden/double_guard",
    element: <HiddenSWSettingsPage />,
    name: "特殊作业上报设置",
  },
  {
    path: HiddenRoutes.BasicInfo,
    element: <HiddenBasicInfoSettingsPage />,
    name: "基础信息上报配置",
  },
  {
    path: HiddenRoutes.Sensor,
    element: <HiddenSensorSettingsPage />,
    name: "实时值上报设置",
  },
  {
    path: HiddenRoutes.Equipment,
    element: <HiddenEquipmentSettingsPage />,
    name: "设备上报设置",
  },
  {
    path: `${SpecialWorkRoutes.FORM_CONFIG}/:id`,
    element: <FormConfigPage />,
    name: "作业表单配置",
    hide: true,
  },
  {
    path: `${SpecialWorkRoutes.PRINT_CONFIG}/:id`,
    element: <PrintConfigPage />,
    name: "作业票打印配置",
    hide: true,
  },
  {
    path: SpecialWorkRoutes.JS_TEMPLATE, // "/js_template",
    element: <JsTemplatePage />,
    name: "作业表单配置",
    hide: true,
  },
  {
    path: SpecialWorkRoutes.PREVIEW, //"/preview",
    element: <PreviewPage />,
    name: "表单预览(demo)",
    hide: true,
  },
];
