// 路由模块统一导出文件
// 此文件将在后续步骤中逐步填充各个模块的导出

import { AlarmAnalysisMap, AlarmProcessMap, AlarmSettingsMap } from "./alarm";
import {
  BasicContractorMap,
  DocumentManagmentMap,
  EnterpriseCertificateMap,
  EnterpriseInformationMap,
  EquipmentFacilitiesMap,
  HumanResourceMap,
  ProductiveProcessMap,
  ProjectThreeSyncMap,
} from "./baseSetting";
import {
  ContractorBasicMap,
  ContractorEntryMap,
  ContractorEvaluationMap,
  ContractorProjectMap,
} from "./contractor";
import {
  BbTaskMap,
  CheckPlanMap,
  EnterpriseSelfInspectionMap,
  GovSupervisionMap,
  IncentiveMap,
  ManageCardMap,
  RiskMap,
  SnapMap,
} from "./doubleGuard";
import { EmergencyManagementMap } from "./emergency";
import { EquipmentArchiveMap, EquipmentManagementMap } from "./equipment";
import { IntelligentInspectionMap } from "./intelligentInspection";
import { DataVisulizationMap, OnlineMonitorAlertMap } from "./majorHazard";
import {
  AIMap,
  EnergyManagementMap,
  EnvironmentManagementMap,
  FireFighterModuleMap,
  MessageMap,
  PersonnelLocationMap,
} from "./misc";
import { JobTmplMap, SafetyMeasureMap, Ticket } from "./specialWork";
import { HiddenMenu, SystemMap } from "./system";
import {
  MyTrainingLayoutMap,
  StudentManagementMap,
  TrainingManagementMap,
  TrainingMaterialMap,
} from "./training";

export const RoleRouter = [
  ...RiskMap,
  ...HumanResourceMap,
  ...ManageCardMap,
  ...BbTaskMap,
  ...CheckPlanMap,
  ...IncentiveMap,
  ...SnapMap,
  ...GovSupervisionMap,
  ...MessageMap,
  ...Ticket,
  ...SafetyMeasureMap,
  ...JobTmplMap,
  ...OnlineMonitorAlertMap,
  ...DataVisulizationMap,
  ...EnterpriseCertificateMap,
  ...ProductiveProcessMap,
  ...EquipmentFacilitiesMap,
  ...EmergencyManagementMap,
  ...BasicContractorMap,
  ...DocumentManagmentMap,
  ...ProjectThreeSyncMap,
  ...SystemMap,
  ...PersonnelLocationMap,
  ...AIMap,
  ...IntelligentInspectionMap,
  ...TrainingMaterialMap,
  ...TrainingManagementMap,
  ...StudentManagementMap,
  ...MyTrainingLayoutMap,
  ...EnterpriseInformationMap,
  ...EquipmentArchiveMap,
  ...EquipmentManagementMap,
  ...FireFighterModuleMap,
  ...EnergyManagementMap,
  ...EnvironmentManagementMap,
  ...EnterpriseSelfInspectionMap,
  ...ContractorBasicMap,
  ...ContractorEntryMap,
  ...ContractorProjectMap,
  ...ContractorEvaluationMap,
  ...AlarmSettingsMap,
  ...AlarmProcessMap,
  ...AlarmAnalysisMap,
  ...HiddenMenu,
];

// 系统管理模块
export { HiddenMenu, SystemMap } from "./system";

// 应急管理模块
export { EmergencyManagementMap } from "./emergency";

// 培训管理模块
export {
  MyTrainingCertificateMap,
  MyTrainingLayoutMap,
  MyTrainingMap,
  StudentManagementMap,
  TestPaperMap,
  TrainingManagementMap,
  TrainingMaterialMap,
} from "./training";

// 设备管理模块
export { EquipmentArchiveMap, EquipmentManagementMap } from "./equipment";
