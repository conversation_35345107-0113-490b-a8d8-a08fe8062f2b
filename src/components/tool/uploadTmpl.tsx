import { IconInfoCircle, IconUpload } from "@douyinfe/semi-icons";
import { Modal, Toast, Upload } from "@douyinfe/semi-ui";
import { useQueryClient } from "@tanstack/react-query";
import { api_url, base_url } from "config";
import { useBtnHooks } from "hooks";
import { FC, useRef, useState } from "react";
import { useLoaderData, useLocation } from "react-router-dom";

type UploadTmplProps = {
  entity?: string;
  excelType: string;
  downUrl: string;
  tip?: string;
  refetchFn?: { refetch: () => void };
  queryKey?: string[];
};

export const UploadTmpl: FC<UploadTmplProps> = ({
  entity,
  excelType,
  downUrl,
  tip,
  refetchFn,
  queryKey,
}) => {
  const uploadRef = useRef<Upload>(null);
  const queryClient = useQueryClient();
  const [visible, setVisible] = useState(false);
  const [hasFile, setHasFile] = useState(false);
  const action = `${api_url}/system/upload`;

  const loderData = useLoaderData();
  const { pathname } = useLocation();
  const genBtn = useBtnHooks(loderData, pathname);

  let data = {
    excelType: excelType,
  };

  const handleError = (error: Error) => {
    let opts = {
      content: `上传失败!`,
      duration: 2,
    };
    Toast.error(opts);
  };
  const handleSuccess = (responseBody) => {
    console.log(responseBody);
    const excelResult = responseBody.data?.excelResult;
    console.log(excelResult);

    if (excelResult.code !== 0) {
      let opts = {
        content: `解析失败! ${excelResult.message}`,
        duration: 6,
      };
      Toast.error(opts);
    } else {
      let opts = {
        content: `上传且解析成功!`,
        duration: 2,
      };
      queryClient.invalidateQueries({ queryKey: [queryKey] });
      refetchFn?.refetch?.();
      Toast.success(opts);
    }

    setVisible(false);
  };
  const handleOk = () => {
    setVisible(!visible);
  };

  const handleChange = ({ fileList }) => {
    setHasFile(Boolean(fileList?.length));
  };
  const handleUpload = () => {
    uploadRef.current.upload();
  };

  return (
    <div className="">
      <div className="tooltip" data-tip="导入数据">
        {genBtn(
          "import",
          <button
            className="btn btn-sm btn-ghost rounded no-animation"
            role="button"
            onClick={handleOk}
          >
            <IconUpload />
          </button>
        )}
      </div>
      <Modal
        title={entity ? `${entity}导入` : "导入数据"}
        visible={visible}
        onOk={handleOk}
        onCancel={handleOk}
        footer={
          <div className="flex gap-2 justify-end">
            {hasFile ? (
              <button
                className="btn rounded btn-primary btn-sm"
                onClick={handleUpload}
              >
                确定
              </button>
            ) : null}

            <button className="btn rounded  btn-sm" onClick={handleOk}>
              取消
            </button>
          </div>
        }
      >
        <div className="flex text-sm text-gray-600 mb-2 items-center gap-1">
          <IconInfoCircle />
          请先下载模板,然后按照模板组织数据进行导入
        </div>
        <div className="flex gap-2 justify-center p-5">
          <a
            href={`${base_url}${downUrl}`}
            target="_blank"
            className="btn btn-sm rounded no-animation"
          >
            下载模板
          </a>
        </div>
        <div className="flex text-sm text-gray-600 mb-2 items-center gap-1">
          <IconInfoCircle />
          上传文件
        </div>
        <Upload
          onChange={handleChange}
          ref={uploadRef}
          limit={1}
          headers={{
            Authorization: `Bearer ${localStorage.getItem("token")}`,
          }}
          uploadTrigger="custom"
          action={action}
          data={data}
          onError={handleError}
          onSuccess={handleSuccess}
          draggable={true}
          dragMainText={`点击上传文件或拖拽文件到这里`}
          // dragSubText={tip}
        />
        {tip ? <div className="text-sm text-gray-600 mt-2">{tip}</div> : null}
      </Modal>
    </div>
  );
};
