import { Modal, TabPane, Tabs, useFormApi } from "@douyinfe/semi-ui";
import {
  certificatePickerAtom,
  certificatePickerDataAtom,
  jobCertificatesReferValues,
} from "atoms";
import { useAtom, useAtomValue } from "jotai";
import { useResetAtom } from "jotai/utils";
import {
  CertificateContent,
  CertificateFilter,
  ContractorEmployeeCertificateContent,
  ContractorEmployeeCertificateFilter,
} from "pages/basicInfo/content";
import { useCallback, useEffect, useState } from "react";

export const CertificateTableModal = () => {
  const formApi = useFormApi();
  const [list, setList] = useState<{ id: string | number; type: 1 | 2 }[]>([]);
  const [record1, setRecord1] = useState([]);
  const [record2, setRecord2] = useState([]);
  const [initRows1, setInitRows1] = useState([]);
  const [initRows2, setInitRows2] = useState([]);
  const [tab, setTab] = useState(1);
  const [show, setShow] = useAtom(certificatePickerAtom);
  const [data, setData] = useAtom(certificatePickerDataAtom);
  const reset = useResetAtom(certificatePickerAtom);
  const jobCertificatesReferValue = useAtomValue(jobCertificatesReferValues);

  const handleSave = () => {
    const tmp: any = [];
    const record: any[] = [];
    record1.forEach((o) => {
      record.push({
        ...o,
        type: 1,
      });
    });
    record2.forEach((o) => {
      record.push({
        ...o,
        type: 2,
      });
    });
    // console.debug(record, "record");
    list?.forEach((i) => {
      console.debug("----list item", i);
      const item = record.find((r) => r.id === i.id && r.type === i.type);
      if (item) {
        console.debug("----find item", item);
        tmp.push(item);
      } else {
        console.debug("----not find item", item);
      }
    });

    setData({
      record: tmp,
    });
    const formList = [];
    tmp.forEach((i: any) => {
      formList.push({
        type: i.type,
        id: i?.id,
      });
    });
    formApi.setValue("jobCertificateInfo", formList);
    reset();
  };

  useEffect(() => {
    if (jobCertificatesReferValue?.length) {
      const r1 = [];
      const r2 = [];
      jobCertificatesReferValue.forEach((o) => {
        if (o?.type == 1) {
          r1.push(o);
        } else {
          r2.push(o);
        }
      });
      setInitRows1(r1);
      setInitRows2(r2);
      console.debug("----set record1", r1);
      setRecord1(r1);
      setRecord2(r2);
    }
  }, [jobCertificatesReferValue]);

  const initRecord = (type: 1 | 2, record: any[]) => {
    if (type == 1) {
      console.debug("----init record1", record);
      // 应该是原有record1和record的并集
      setRecord1([...record, ...record1]);
    } else {
      setRecord2([...record, ...record2]);
    }
  };

  // 添加/删除，数据同步相关直接在关闭modal时做，不然多处同步，太乱
  const handleSetRecord = useCallback(
    (ids: (string | number)[], type?: 1 | 2) => {
      if (!type) return;

      const currentTypeIds = ids.map((id) => ({ id, type }));
      const otherTypeItems = list.filter((item) => item.type !== type);
      // console.debug("----currentTypeIds", currentTypeIds);
      // console.debug("----otherTypeItems", otherTypeItems);

      setList([...otherTypeItems, ...currentTypeIds]);
    },
    [setList, list]
  );

  return (
    <Modal
      title={"选择持证作业人员"}
      visible={show}
      keepDOM
      width={1200}
      onCancel={() => {
        reset();
      }}
      footer={
        <div className="flex gap-2 justify-end">
          <button
            className="btn rounded btn-sm"
            onClick={() => {
              reset();
            }}
          >
            取消
          </button>
          <button
            className="btn rounded btn-primary btn-sm"
            onClick={handleSave}
          >
            确定
          </button>
        </div>
      }
      centered
    >
      <Tabs
        type="line"
        onChange={(v) => {
          setTab(parseInt(v));
        }}
      >
        <TabPane tab="本厂" itemKey="1">
          <div className="flex flex-col gap-4 ">
            <CertificateFilter filter={{ isValid: 1 }} isValidDisabled={true} />
            {/* isValid: 1 有效 */}
            <div className="grid">
              <CertificateContent
                mode="modal"
                cb={handleSetRecord}
                initData={initRecord}
                initRows={initRows1}
                filter={{
                  isValid: 1, // 有效
                }}
              />
            </div>
          </div>
        </TabPane>
        <TabPane tab="承包商" itemKey="2">
          <div className="flex flex-col gap-4 ">
            <ContractorEmployeeCertificateFilter
              filter={{ isValid: 1 }}
              isValidDisabled={true}
            />
            <div className="grid">
              <ContractorEmployeeCertificateContent
                mode="modal"
                cb={handleSetRecord}
                initData={initRecord}
                initRows={initRows2}
                filter={{
                  isValid: 1, // 有效
                }}
              />
            </div>
          </div>
        </TabPane>
      </Tabs>
    </Modal>
  );
};
