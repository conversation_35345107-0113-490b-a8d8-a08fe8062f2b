/**
 * 空状态组件
 * 用于展示无数据或空内容的状态
 */

import { Button } from "@douyinfe/semi-ui";
import { ReactNode } from "react";

interface EmptyStateProps {
  title?: string;
  description?: string;
  icon?: ReactNode;
  action?: {
    text: string;
    onClick: () => void;
  };
  className?: string;
}

const EmptyState = ({
  title = "暂无数据",
  description = "当前没有可显示的内容",
  icon,
  action,
  className = "",
}: EmptyStateProps) => {
  return (
    <div
      className={`flex flex-col items-center justify-center py-12 px-6 text-center ${className}`}
    >
      {icon && <div className="mb-4 text-gray-300">{icon}</div>}

      <div className="text-lg font-medium text-gray-600 mb-2">{title}</div>

      <div className="text-sm text-gray-400 mb-6 max-w-md">{description}</div>

      {action && (
        <Button onClick={action.onClick} type="primary" size="small">
          {action.text}
        </Button>
      )}
    </div>
  );
};

/**
 * 特种作业数据空状态组件
 */
const SpecialWorkEmptyState = ({ onRetry }: { onRetry?: () => void }) => (
  <EmptyState
    title="暂无作业数据"
    description="该项目暂无特种作业分类数据，请检查数据源或稍后重试"
    icon={
      <svg
        className="w-16 h-16"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={1.5}
          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
        />
      </svg>
    }
    action={
      onRetry
        ? {
            text: "重新加载",
            onClick: onRetry,
          }
        : undefined
    }
  />
);

export { EmptyState, SpecialWorkEmptyState };
export type { EmptyStateProps };
