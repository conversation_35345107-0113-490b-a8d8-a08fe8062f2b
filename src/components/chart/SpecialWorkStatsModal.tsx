import { Modal, Spin } from "@douyinfe/semi-ui";
import { useCallback, useEffect, useRef } from "react";
import { useErrorHandler } from "../../hooks/useErrorHandler";
import { useLoadingState } from "../../hooks/useLoadingState";
import type { SpecialWorkStatsModalProps } from "../../types/drilldown";
import { getSafeCategoryInfo } from "../../utils/drilldownValidation";
import { ErrorBoundary } from "../error/ErrorBoundary";
import { SpecialWorkEmptyState } from "../ui/EmptyState";
import { PieChart, buildPieCenterContent, buildPieOption } from "./PieChart";

/**
 * 特种作业统计下钻模态框组件
 * 用于展示特定区域/部门/承包商的作业票分类饼图统计
 * @param isOpen - 模态框是否打开
 * @param onClose - 关闭模态框的回调函数
 * @param title - 模态框标题（如：某某区域 - 作业分类统计）
 * @param categoryInfo - 作业分类统计数据
 */
const SpecialWorkStatsModal = ({
  isOpen,
  onClose,
  title,
  categoryInfo,
}: SpecialWorkStatsModalProps) => {
  // 使用错误处理 Hook
  const { errorState, handleError, clearError, showErrorToast } =
    useErrorHandler();

  // 使用加载状态 Hook
  const { isLoading, startLoading, stopLoading } = useLoadingState({
    initialLoading: false,
    autoReset: true,
    resetDelay: 300,
  });

  // 保存原始滚动状态的引用
  const originalOverflowRef = useRef<string>("");

  // 优化的关闭处理函数
  const handleClose = useCallback(() => {
    // 恢复页面滚动
    if (originalOverflowRef.current) {
      document.body.style.overflow = originalOverflowRef.current;
    } else {
      document.body.style.overflow = "unset";
    }
    onClose();
  }, [onClose]);

  // 处理键盘事件和页面滚动控制
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Escape" && isOpen) {
        event.preventDefault();
        handleClose();
      }
    };

    if (isOpen) {
      // 保存当前滚动状态
      originalOverflowRef.current = document.body.style.overflow || "";

      // 禁止页面滚动
      document.body.style.overflow = "hidden";

      // 添加键盘事件监听
      document.addEventListener("keydown", handleKeyDown, { passive: false });

      // 防止焦点丢失，确保模态框可以接收键盘事件
      const modalElement = document.querySelector(".special-work-stats-modal");
      if (modalElement) {
        (modalElement as HTMLElement).focus();
      }
    }

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
      // 清理时恢复页面滚动
      if (originalOverflowRef.current) {
        document.body.style.overflow = originalOverflowRef.current;
      } else {
        document.body.style.overflow = "unset";
      }
    };
  }, [isOpen, handleClose]);

  // 数据验证和处理
  const {
    data: validatedData,
    error: validationError,
    isEmpty,
  } = getSafeCategoryInfo(categoryInfo);

  // 处理数据验证和加载状态
  useEffect(() => {
    if (isOpen) {
      // 清除之前的错误状态
      clearError();

      if (categoryInfo) {
        startLoading();

        // 检查数据验证错误
        if (validationError) {
          handleError(validationError, "validation");
          showErrorToast(`数据验证失败: ${validationError}`);
          stopLoading();
        } else {
          // 模拟数据处理延迟
          const timer = setTimeout(() => {
            stopLoading();
          }, 300);

          return () => clearTimeout(timer);
        }
      }
    }
  }, [
    isOpen,
    categoryInfo,
    validationError,
    clearError,
    startLoading,
    stopLoading,
    handleError,
    showErrorToast,
  ]);

  return (
    <Modal
      title={title}
      visible={isOpen}
      onCancel={handleClose}
      footer={null}
      width="90%"
      style={{ maxWidth: "600px" }}
      centered
      maskClosable={true}
      closable={true}
      className="special-work-stats-modal"
      bodyStyle={{
        padding: "16px",
        maxHeight: "80vh",
        overflow: "auto",
      }}
      // 响应式设计：移动端适配
      wrapClassName="special-work-modal-wrap"
      // 确保点击外部区域可以关闭
      onMaskClick={handleClose}
      // 添加动画效果
      motion={{
        enter: {
          opacity: [0, 1],
          scale: [0.8, 1],
        },
        exit: {
          opacity: [1, 0],
          scale: [1, 0.8],
        },
      }}
    >
      <ErrorBoundary
        onError={(error, errorInfo) => {
          console.error("SpecialWorkStatsModal Error:", error, errorInfo);
          handleError(error.message, "unknown");
        }}
      >
        <div className="flex flex-col items-center justify-center min-h-[400px] relative">
          {isLoading ? (
            <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-80 z-10">
              <Spin size="large" tip="正在加载数据..." />
            </div>
          ) : null}

          {errorState.hasError ? (
            <div className="flex items-center justify-center h-64 text-red-400 error-state">
              <div className="text-center px-4">
                <div className="text-lg mb-2">
                  {errorState.errorType === "validation"
                    ? "数据验证失败"
                    : "数据加载失败"}
                </div>
                <div className="text-sm mb-4">{errorState.errorMessage}</div>
                <div className="flex flex-col sm:flex-row gap-2 sm:gap-4 justify-center">
                  <button
                    onClick={() => {
                      clearError();
                      // 可以在这里添加重试逻辑
                    }}
                    className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors touch-manipulation"
                  >
                    重试
                  </button>
                  <button
                    onClick={handleClose}
                    className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors touch-manipulation"
                  >
                    关闭
                  </button>
                </div>
              </div>
            </div>
          ) : isEmpty ? (
            <div className="empty-state">
              <SpecialWorkEmptyState
                onRetry={() => {
                  clearError();
                  startLoading();
                  // 这里可以添加重新获取数据的逻辑
                  setTimeout(() => {
                    stopLoading();
                  }, 1000);
                }}
              />
            </div>
          ) : validatedData ? (
            <div className="w-full pie-chart-container">
              <PieChart
                title="作业分类统计"
                queryKey={[]}
                queryFn={() => {
                  // 过滤掉计数为0的项
                  const filteredData = validatedData.jobCategoryStats.filter(
                    (item: { count: number }) => item.count > 0
                  );
                  return Promise.resolve({ data: filteredData });
                }}
                optionBuilder={buildPieOption({
                  nameField: "name",
                  valueField: "count",
                  seriesName: "作业分类统计", // 设置系列名称
                  radius: ["40%", "70%"],
                  center: ["50%", "50%"],
                  colorList: [
                    "#60B7FF",
                    "#36C361",
                    "#FFB84D",
                    "#FF6B6B",
                    "#9C88FF",
                    "#FF9F43",
                    "#26D0CE",
                    "#FD79A8",
                  ],
                  legend: {
                    orient: "vertical",
                    left: "right",
                    top: "middle",
                    itemWidth: 12,
                    itemHeight: 12,
                    textStyle: {
                      fontSize: 12,
                    },
                    // 移动端图例适配
                    responsive: true,
                  },
                  tooltip: {
                    trigger: "item",
                    formatter: "{a} <br/>{b}: {c} ({d}%)",
                    // 移动端 tooltip 适配
                    confine: true,
                  },
                  // 响应式配置
                  responsive: true,
                })}
                centerContent={buildPieCenterContent({
                  totalField: "count",
                  label: "作业总计",
                })}
                height={400}
              />

              {/* 数据质量提示 */}
              {validatedData.jobCategoryStats.length <
                (categoryInfo as any)?.jobCategoryStats?.length && (
                <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <div className="text-sm text-yellow-700">
                    <strong>数据提示：</strong>
                    部分数据格式不正确已被过滤，显示{" "}
                    {validatedData.jobCategoryStats.length} 项有效数据
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="flex items-center justify-center h-64 text-gray-400">
              <div className="text-center">
                <div className="text-lg mb-2">数据处理中</div>
                <div className="text-sm">请稍候...</div>
              </div>
            </div>
          )}
        </div>
      </ErrorBoundary>
    </Modal>
  );
};

export { SpecialWorkStatsModal };
export type { SpecialWorkStatsModalProps };

// 添加全局样式以确保响应式设计
if (typeof document !== "undefined") {
  const styleId = "special-work-modal-styles";
  if (!document.getElementById(styleId)) {
    const style = document.createElement("style");
    style.id = styleId;
    style.textContent = `
      .special-work-modal-wrap {
        padding: 16px;
      }
      
      .special-work-stats-modal {
        max-width: 90vw !important;
        max-height: 90vh !important;
      }
      
      .special-work-stats-modal .semi-modal-content {
        max-height: 90vh;
        overflow: hidden;
        border-radius: 8px;
      }
      
      .special-work-stats-modal .semi-modal-body {
        max-height: calc(90vh - 120px);
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
      }
      
      /* 移动端适配 */
      @media (max-width: 768px) {
        .special-work-modal-wrap {
          padding: 8px;
        }
        
        .special-work-stats-modal {
          width: 95vw !important;
          max-width: 95vw !important;
          margin: 0 !important;
        }
        
        .special-work-stats-modal .semi-modal-body {
          padding: 12px !important;
          max-height: calc(90vh - 100px);
        }
        
        .special-work-stats-modal .semi-modal-header {
          padding: 12px 16px !important;
          font-size: 16px;
        }
        
        /* 饼图在移动端的适配 */
        .special-work-stats-modal .pie-chart-container {
          height: 300px !important;
        }
      }
      
      /* 小屏幕设备适配 */
      @media (max-width: 480px) {
        .special-work-stats-modal {
          width: 98vw !important;
          max-width: 98vw !important;
        }
        
        .special-work-stats-modal .semi-modal-body {
          padding: 8px !important;
        }
        
        .special-work-stats-modal .pie-chart-container {
          height: 250px !important;
        }
        
        /* 错误状态和空状态的移动端适配 */
        .special-work-stats-modal .error-state,
        .special-work-stats-modal .empty-state {
          padding: 16px 8px;
        }
        
        .special-work-stats-modal .error-state button,
        .special-work-stats-modal .empty-state button {
          padding: 8px 16px;
          font-size: 14px;
        }
      }
      
      /* 确保模态框在所有设备上都能正确显示 */
      @media (max-height: 600px) {
        .special-work-stats-modal .semi-modal-content {
          max-height: 95vh;
        }
        
        .special-work-stats-modal .semi-modal-body {
          max-height: calc(95vh - 80px);
        }
      }
      
      /* 横屏模式适配 */
      @media (orientation: landscape) and (max-height: 500px) {
        .special-work-stats-modal {
          width: 80vw !important;
          max-width: 80vw !important;
        }
        
        .special-work-stats-modal .semi-modal-body {
          max-height: calc(95vh - 60px);
        }
        
        .special-work-stats-modal .pie-chart-container {
          height: 200px !important;
        }
      }
    `;
    document.head.appendChild(style);
  }
}
