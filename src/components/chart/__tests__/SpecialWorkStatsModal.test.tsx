import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { fireEvent, render, screen } from "@testing-library/react";
import React from "react";
import { vi } from "vitest";
import type { SpecialWorkCategoryInfo } from "../../../types/drilldown";
import { SpecialWorkStatsModal } from "../SpecialWorkStatsModal";

const mockSpecialWorkData: SpecialWorkCategoryInfo = {
  total: 150,
  jobCategoryStats: [
    { id: 1, name: "高处作业", count: 45, icon: "height" },
    { id: 2, name: "动火作业", count: 38, icon: "fire" },
    { id: 3, name: "受限空间", count: 32, icon: "space" },
    { id: 4, name: "吊装作业", count: 25, icon: "lift" },
    { id: 5, name: "其他特种作业", count: 10, icon: "other" },
  ],
};

describe("SpecialWorkStatsModal", () => {
  const mockOnClose = vi.fn();
  let queryClient: QueryClient;

  beforeEach(() => {
    mockOnClose.mockClear();
    queryClient = new QueryClient({
      defaultOptions: {
        queries: {
          retry: false,
        },
      },
    });
  });

  const renderWithQueryClient = (component: React.ReactElement) => {
    return render(
      <QueryClientProvider client={queryClient}>
        {component}
      </QueryClientProvider>
    );
  };

  it("renders special work stats modal correctly when open with data", async () => {
    renderWithQueryClient(
      <SpecialWorkStatsModal
        isOpen={true}
        onClose={mockOnClose}
        title="生产区域A - 特种作业分类统计"
        categoryInfo={mockSpecialWorkData}
      />
    );

    expect(
      screen.getByText("生产区域A - 特种作业分类统计")
    ).toBeInTheDocument();
    expect(screen.getByText("作业分类统计")).toBeInTheDocument();

    // Wait for the chart to load
    await screen.findByText("作业总计");
    expect(screen.getByText("作业总计")).toBeInTheDocument();
    expect(screen.getByText("150")).toBeInTheDocument();
  });

  it("shows empty state when no special work data", () => {
    renderWithQueryClient(
      <SpecialWorkStatsModal
        isOpen={true}
        onClose={mockOnClose}
        title="某部门 - 特种作业分类统计"
        categoryInfo={null}
      />
    );

    expect(screen.getByText("暂无作业数据")).toBeInTheDocument();
    expect(
      screen.getByText("该项目暂无特种作业分类数据，请检查数据源或稍后重试")
    ).toBeInTheDocument();
  });

  it("does not render when modal is closed", () => {
    renderWithQueryClient(
      <SpecialWorkStatsModal
        isOpen={false}
        onClose={mockOnClose}
        title="测试标题"
        categoryInfo={mockSpecialWorkData}
      />
    );

    expect(screen.queryByText("测试标题")).not.toBeInTheDocument();
  });

  it("calls onClose when ESC key is pressed", () => {
    renderWithQueryClient(
      <SpecialWorkStatsModal
        isOpen={true}
        onClose={mockOnClose}
        title="测试标题"
        categoryInfo={mockSpecialWorkData}
      />
    );

    fireEvent.keyDown(document, { key: "Escape" });
    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });

  it("validates and filters invalid special work data", async () => {
    const invalidSpecialWorkData = {
      total: 80,
      jobCategoryStats: [
        { id: 1, name: "高处作业", count: 30, icon: "height" },
        { id: 2, name: null, count: 20, icon: "fire" }, // 无效：name 为 null
        { id: 3, name: "受限空间", count: -5, icon: "space" }, // 无效：count 为负数
        { id: 4, name: "动火作业", count: "invalid", icon: "fire" }, // 无效：count 不是数字
        { id: 5, name: "吊装作业", count: 15, icon: "lift" },
      ],
    };

    renderWithQueryClient(
      <SpecialWorkStatsModal
        isOpen={true}
        onClose={mockOnClose}
        title="测试标题"
        categoryInfo={invalidSpecialWorkData}
      />
    );

    // 应该显示PieChart组件，因为有有效数据（高处作业和吊装作业）
    expect(screen.getByText("作业分类统计")).toBeInTheDocument();

    // Wait for the chart to load
    await screen.findByText("作业总计");
    expect(screen.getByText("作业总计")).toBeInTheDocument();
    // 总计应该是有效数据的总和：30 + 15 = 45
    expect(screen.getByText("45")).toBeInTheDocument();
  });

  it("handles empty jobCategoryStats array", () => {
    const emptyData = {
      total: 0,
      jobCategoryStats: [],
    };

    renderWithQueryClient(
      <SpecialWorkStatsModal
        isOpen={true}
        onClose={mockOnClose}
        title="空数据测试"
        categoryInfo={emptyData}
      />
    );

    expect(screen.getByText("暂无作业数据")).toBeInTheDocument();
    expect(
      screen.getByText("该项目暂无特种作业分类数据，请检查数据源或稍后重试")
    ).toBeInTheDocument();
  });
});
