import { useQuery } from "@tanstack/react-query";
import { Echart } from "pages/bigScreen/component/Chart";
import React, { useMemo, useState } from "react";

export interface PieChartTab {
  label: string;
  value: any;
}

export interface PieChartProps {
  title: string;
  queryKey: any[];
  queryFn: (params: any) => Promise<any>;
  filter?: object;
  tabList?: PieChartTab[];
  tabParamName?: string; // 默认为'type'
  optionBuilder: (data: any, tabValue?: any) => object;
  centerContent?: (data: any, tabValue?: any) => React.ReactNode;
  height?: number;
  onItemClick?: (params: {
    name: string;
    value: number;
    dataIndex: number;
  }) => void;
}

export function PieChart({
  title,
  queryKey,
  queryFn,
  filter = {},
  tabList,
  tabParamName = "type",
  optionBuilder,
  centerContent,
  height = 300,
  onItemClick,
}: PieChartProps) {
  // tab切换状态
  const [tabValue, setTabValue] = useState(tabList?.[0]?.value);

  // 组合请求参数
  const params = useMemo(() => {
    if (tabList && tabParamName) {
      return { ...filter, [tabParamName]: tabValue };
    }
    return filter;
  }, [filter, tabList, tabParamName, tabValue]);

  // useQuery拉取数据
  const { data, isLoading } = useQuery({
    queryKey: [...queryKey, params],
    queryFn: () => queryFn(params),
    enabled: !tabList || tabValue !== undefined,
  });

  // 生成option
  const option = useMemo(
    () => optionBuilder(data, tabValue),
    [data, tabValue, optionBuilder]
  );

  // 判断empty状态：series无数据
  const isEmpty = useMemo(() => {
    const opt = option as any;
    if (!opt || !opt.series) return true;
    const seriesArr = Array.isArray(opt.series) ? opt.series : [opt.series];
    return seriesArr.every((s: any) => !s.data || s.data.length === 0);
  }, [option]);

  // 处理点击事件
  const handleChartClick = (params: any) => {
    if (onItemClick && params.componentType === "series") {
      onItemClick({
        name: params.name,
        value: params.value,
        dataIndex: params.dataIndex,
      });
    }
  };

  return (
    <div className="flex flex-col bg-white border border-[#EDEDEE] rounded-lg p-6 divide-y gap-y-5 relative">
      <div className="flex justify-between">
        <div className="flex items-center gap-x-[10px]">
          <div className="bg-[#60B7FF] w-1 h-[22px]"></div>
          <span className="text-[18px] font-bold">{title}</span>
        </div>
        {tabList && tabList.length > 0 && (
          <div className="flex gap-2">
            <div className="border border-[#60B7FF] text-[#60B7FF] text-sm cursor-pointer overflow-hidden rounded-md flex">
              {tabList.map((tab) => (
                <div
                  key={tab.value}
                  className={`leading-none px-[14px] py-[8px] ${tabValue === tab.value ? "text-white bg-[#60B7FF]" : ""}`}
                  onClick={() => setTabValue(tab.value)}
                >
                  {tab.label}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
      <div style={{ width: "100%", height, position: "relative" }}>
        {isLoading ? (
          <div className="flex items-center justify-center h-full text-gray-400">
            加载中...
          </div>
        ) : isEmpty ? (
          <div className="flex items-center justify-center h-full text-gray-400">
            暂无数据
          </div>
        ) : (
          <>
            <Echart option={option} onClick={handleChartClick} />
            {centerContent && (
              <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 z-10 flex flex-col items-center">
                {centerContent(data, tabValue)}
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}

/**
 * 通用饼图option生成工厂
 */
export function buildPieOption(config: {
  nameField: string;
  valueField: string;
  colorList?: string[];
  legend?: object;
  radius?: [string, string];
  center?: [string, string];
  label?: object;
  tooltip?: object;
  seriesName?: string; // 新增：系列名称配置
}) {
  return (data: any) => {
    const list = data?.data ?? [];
    return {
      tooltip: config.tooltip || { trigger: "item" },
      legend: config.legend || {
        orient: "vertical",
        left: "right",
        top: "middle",
      },
      ...(config.colorList ? { color: config.colorList } : {}),
      series: [
        {
          name: config.seriesName || "数据统计", // 设置系列名称
          type: "pie",
          radius: config.radius || ["40%", "70%"],
          ...(config.center ? { center: config.center } : {}),
          ...(config.label ? { label: config.label } : {}),
          data: list.map((i: any) => ({
            value: i[config.valueField],
            name: i[config.nameField],
          })),
        },
      ],
    };
  };
}

/**
 * 通用饼图中心内容生成工厂
 */
export function buildPieCenterContent(config: {
  totalField: string;
  label?: string;
}) {
  return (data: any) => {
    const list = data?.data ?? [];
    const total = list.reduce(
      (sum: number, i: any) => sum + (i[config.totalField] ?? 0),
      0
    );
    return (
      <div className="text-center">
        <div className="text-base text-[#666]">{config.label || "总计"}</div>
        <div className="text-[30px] text-[#333] font-black">{total}</div>
      </div>
    );
  };
}
