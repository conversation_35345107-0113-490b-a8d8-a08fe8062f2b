/**
 * 加载状态管理 Hook
 * 用于管理组件的加载状态和相关逻辑
 */

import { useCallback, useEffect, useState } from "react";

interface UseLoadingStateOptions {
  initialLoading?: boolean;
  autoReset?: boolean;
  resetDelay?: number;
}

interface UseLoadingStateReturn {
  isLoading: boolean;
  startLoading: () => void;
  stopLoading: () => void;
  setLoading: (loading: boolean) => void;
}

export function useLoadingState(
  options: UseLoadingStateOptions = {}
): UseLoadingStateReturn {
  const {
    initialLoading = false,
    autoReset = false,
    resetDelay = 300,
  } = options;

  const [isLoading, setIsLoading] = useState(initialLoading);

  const startLoading = useCallback(() => {
    setIsLoading(true);
  }, []);

  const stopLoading = useCallback(() => {
    setIsLoading(false);
  }, []);

  const setLoading = useCallback((loading: boolean) => {
    setIsLoading(loading);
  }, []);

  // 自动重置加载状态
  useEffect(() => {
    if (autoReset && isLoading) {
      const timer = setTimeout(() => {
        setIsLoading(false);
      }, resetDelay);

      return () => clearTimeout(timer);
    }
  }, [isLoading, autoReset, resetDelay]);

  return {
    isLoading,
    startLoading,
    stopLoading,
    setLoading,
  };
}
