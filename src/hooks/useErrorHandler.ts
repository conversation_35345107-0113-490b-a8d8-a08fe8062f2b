/**
 * 错误处理 Hook
 * 用于统一管理组件中的错误状态和错误处理逻辑
 */

import { Toast } from "@douyinfe/semi-ui";
import { useCallback, useState } from "react";

interface ErrorState {
  hasError: boolean;
  errorMessage: string | null;
  errorType: "validation" | "network" | "unknown" | null;
}

interface UseErrorHandlerReturn {
  errorState: ErrorState;
  handleError: (error: string | Error, type?: ErrorState["errorType"]) => void;
  clearError: () => void;
  showErrorToast: (message: string, duration?: number) => void;
}

export function useErrorHandler(): UseErrorHandlerReturn {
  const [errorState, setErrorState] = useState<ErrorState>({
    hasError: false,
    errorMessage: null,
    errorType: null,
  });

  const handleError = useCallback(
    (error: string | Error, type: ErrorState["errorType"] = "unknown") => {
      const errorMessage = error instanceof Error ? error.message : error;

      setErrorState({
        hasError: true,
        errorMessage,
        errorType: type,
      });
    },
    []
  );

  const clearError = useCallback(() => {
    setErrorState({
      hasError: false,
      errorMessage: null,
      errorType: null,
    });
  }, []);

  const showErrorToast = useCallback((message: string, duration = 3) => {
    Toast.error({
      content: message,
      duration,
      showClose: true,
    });
  }, []);

  return {
    errorState,
    handleError,
    clearError,
    showErrorToast,
  };
}
