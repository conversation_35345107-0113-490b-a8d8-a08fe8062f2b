import { useQuery } from "@tanstack/react-query";
import { getHiddenSettings, getSystemExternalLinkList } from "api";
import { useMemo } from "react";
import { ExternalLink, HiddenSettings } from "types";
import { listPageSizeWithoutPaging } from "utils";

// 隐藏设置类型定义

export const useHiddenSettings = (): HiddenSettings => {
  const { data } = useQuery({
    queryKey: ["getHiddenSettings"],
    queryFn: () => {
      return getHiddenSettings();
    },
  });

  const hiddenSettings = useMemo(() => {
    return (data?.data || {}) as HiddenSettings;
  }, [data]);

  return hiddenSettings;
};

export const useExternalLink = (): ExternalLink[] => {
  const { data } = useQuery({
    queryKey: ["getExternalLink"],
    queryFn: () => {
      return getSystemExternalLinkList({
        page: 1,
        pageSize: listPageSizeWithoutPaging,
      });
    },
  });

  const externalLink = useMemo(() => {
    return (data?.data?.results || []) as ExternalLink[];
  }, [data]);

  return externalLink;
};
