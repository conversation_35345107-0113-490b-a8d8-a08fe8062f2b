import { useQuery } from "@tanstack/react-query";
import { getProductionUnitList } from "api";
import { useMemo } from "react";
import { listPageSizeWithoutPaging } from "utils";

export function useProductionUnitOptions(apiFilter = {}) {
  const { data: productionUnitList } = useQuery({
    queryKey: ["getProductionUnitList", apiFilter],
    queryFn: () =>
      getProductionUnitList({
        pageNumber: 1,
        pageSize: listPageSizeWithoutPaging,
        filter: apiFilter,
      }),
  });

  const productionUnitListOptions = useMemo(() => {
    return productionUnitList?.data?.results ?? [];
  }, [productionUnitList]);

  return productionUnitListOptions;
}
