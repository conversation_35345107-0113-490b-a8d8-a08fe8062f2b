import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/basic_info_management/production_unit/search

export const getProductionUnitList = async (params) => {
  const base_url = "/basic_info_management/production_unit/search";
  const res = await post(base_url, params);
  return res;
};

export const getProductionUnit = async (id) => {
  const base_url = `/basic_info_management/production_unit/${id}`;
  return await get(base_url);
};

export const createProductionUnit = async (params) => {
  const res = await post("/basic_info_management/production_unit", params);
  return res;
};

export const delProductionUnit = async (id: number) => {
  const res = await del(`/basic_info_management/production_unit/${id}`);
  return res;
};

export const delProductionUnits = async (ids) => {
  const res = await del(`/basic_info_management/production_unit`, ids);
  return res;
};

export const updateProductionUnit = async (params) => {
  const res = await put(
    `/basic_info_management/production_unit/${params.id}`,
    params?.values
  );
  return res;
};

export const applyMaintenanceProductionUnit = async (params) => {
  const res = await post(
    `/basic_info_management/production_unit/${params.id}/maintenance`,
    params?.values
  );
  return res;
};

export const finishMaintenanceProductionUnit = async (params) => {
  const res = await post(
    `/basic_info_management/production_unit/${params.id}/maintenance/finish`,
    params?.values
  );
  return res;
};

export const getProductionUnitMaintenanceList = async (params) => {
  const base_url = `/basic_info_management/production_unit/${params?.id}/maintenance/search`;
  return await post(base_url, params?.values);
};

export const startProductionUnit = async (params) => {
  const res = await post(
    `/basic_info_management/production_unit/${params.id}/start`,
    params?.values
  );
  return res;
};

export const startFinishProductionUnit = async (params) => {
  const res = await post(
    `/basic_info_management/production_unit/${params.id}/start/finish`,
    params?.values
  );
  return res;
};

export const stopProductionUnit = async (params) => {
  const res = await post(
    `/basic_info_management/production_unit/${params.id}/stop`,
    params?.values
  );
  return res;
};

export const stopFinishProductionUnit = async (params) => {
  const res = await post(
    `/basic_info_management/production_unit/${params.id}/stop/finish`,
    params?.values
  );
  return res;
};

export const getProductionUnitStartStopList = async (params) => {
  const base_url = `/basic_info_management/production_unit/${params?.id}/start_stop/search`;
  return await post(base_url, params?.values);
};

export const productionUnitApis: CommonApis = {
  entity: "ProductionUnit",
  query: getProductionUnitList,
  create: createProductionUnit,
  remove: delProductionUnit,
  removes: delProductionUnits,
  update: updateProductionUnit,
  get: getProductionUnit,
};
