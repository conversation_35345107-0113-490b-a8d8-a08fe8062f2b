import { del, get, post, put } from "api";
import { CommonApis } from "types";

// /v1/basic_info_management/project_three_sync/search

export const getBasicInfoProjectThreeSyncList = async (params) => {
  const base_url = "/basic_info_management/project_three_sync/search";
  const res = await post(base_url, params);
  return res;
};

export const getBasicInfoProjectThreeSync = async (id) => {
  const base_url = `/basic_info_management/project_three_sync/${id}`;
  return await get(base_url);
};

export const createBasicInfoProjectThreeSync = async (params) => {
  const res = await post("/basic_info_management/project_three_sync", params);
  return res;
};

export const delBasicInfoProjectThreeSync = async (id: number) => {
  const res = await del(`/basic_info_management/project_three_sync/${id}`);
  return res;
};

export const delBasicInfoProjectThreeSyncs = async (ids) => {
  const res = await del(`/basic_info_management/project_three_sync`, ids);
  return res;
};

export const updateBasicInfoProjectThreeSync = async (params) => {
  const res = await put(
    `/basic_info_management/project_three_sync/${params.id}`,
    params?.values
  );
  return res;
};

export const applyBasicInfoProjectThreeSync = async (params) => {
  console.log(params);
  const res = await post(
    `/basic_info_management/project_three_sync/${params.id}/apply`,
    params?.values
  );
  return res;
};

export const designBasicInfoProjectThreeSync = async (params) => {
  console.log(params);
  const res = await post(
    `/basic_info_management/project_three_sync/${params.id}/design`,
    params?.values
  );
  return res;
};

export const buildBasicInfoProjectThreeSync = async (params) => {
  console.log(params);
  const res = await post(
    `/basic_info_management/project_three_sync/${params.id}/build`,
    params?.values
  );
  return res;
};

export const testProductionBasicInfoProjectThreeSync = async (params) => {
  console.log(params);
  const res = await post(
    `/basic_info_management/project_three_sync/${params.id}/test_production`,
    params?.values
  );
  return res;
};

export const acceptBasicInfoProjectThreeSync = async (params) => {
  console.log(params);
  const res = await post(
    `/basic_info_management/project_three_sync/${params.id}/accept`,
    params?.values
  );
  return res;
};

export const attachmentBasicInfoProjectThreeSync = async (params) => {
  console.log(params);
  const res = await post(
    `/basic_info_management/project_three_sync/${params.id}/attachment`,
    params?.values
  );
  return res;
};

export const getAttachmentBasicInfoProjectThreeSyncList = async (params) => {
  const base_url = `/basic_info_management/project_three_sync/${params.id}/attachment/search`;
  const res = await post(base_url, params?.values);
  return res;
};

export const basicInfoProjectThreeSyncApis: CommonApis = {
  entity: "BasicInfoProjectThreeSync",
  query: getBasicInfoProjectThreeSyncList,
  create: createBasicInfoProjectThreeSync,
  remove: delBasicInfoProjectThreeSync,
  removes: delBasicInfoProjectThreeSyncs,
  update: updateBasicInfoProjectThreeSync,
  get: getBasicInfoProjectThreeSync,
};
