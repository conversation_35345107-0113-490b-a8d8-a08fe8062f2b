import { get, post } from "api";
import { SpecialWorkCategoryInfo } from "types";

export const getStatCurrentJobSlice = async () => {
  const base_url = "/special_work/current_job_slice";
  const res = await get(base_url);
  return res;
};

// 作业趋势统计参数类型
export interface JobSliceTrendParams {
  type: 1 | 2 | 3; // 1: 近7天, 2: 近30天, 3: 近12月
}

// 作业趋势统计项类型
export interface JobSliceTrendItem {
  year: number;
  month: number;
  day: number;
  num: number;
}

// 作业趋势统计比率项类型
export interface JobSliceTrendRateItem {
  year: number;
  month: number;
  day: number;
  num: number; // 比率值
}

// 作业趋势统计响应数据类型
export interface JobSliceTrendData {
  totalNumList: JobSliceTrendItem[]; // 作业数量
  finishNumList: JobSliceTrendItem[]; // 完成数量
  overtimeNumList: JobSliceTrendItem[]; // 超时数量
  finishRateList: JobSliceTrendRateItem[]; // 完成率
  overtimeRateList: JobSliceTrendRateItem[]; // 超时率
}

//v1/special_work/job_category_job_slice
export const postJobCategoryJobSlice = async (params: any) => {
  const base_url = "/special_work/job_category_job_slice";
  const res = await post(base_url, params);
  return res;
};

export const postJobSliceRank = async (params: any) => {
  const base_url = "/special_work/job_slice_rank";
  const res = await post(base_url, params);
  return res;
};

// API 响应类型定义
export interface JobSliceIndexRankItem {
  area?: { id: number; name: string };
  department?: { id: number; name: string };
  contractor?: { id: number; name: string };
  totalNum: number;
  categoryInfo: SpecialWorkCategoryInfo;
}

//v1/special_work/job_slice_trend
export const postJobSliceTrend = async (params: JobSliceTrendParams) => {
  const base_url = "/special_work/job_slice_trend";
  const res = await post(base_url, params);
  return res;
};
