import { del, get, post, put } from "api";
import { CommonApis, ExternalLinkListRes } from "types";

// /v1/system/external_link/search

export const getSystemExternalLinkList = async (
  params: any,
): Promise<ExternalLinkListRes> => {
  const base_url = "/system/external_link/search";
  const res = await post(base_url, params);
  return res;
};

export const getSystemExternalLink = async (id) => {
  const base_url = `/system/external_link/${id}`;
  return await get(base_url);
};

export const createSystemExternalLink = async (params) => {
  const res = await post("/system/external_link", params);
  return res;
};

export const delSystemExternalLink = async (id: number) => {
  const res = await del(`/system/external_link/${id}`);
  return res;
};

export const delSystemExternalLinks = async (ids) => {
  const res = await del(`/system/external_link`, ids);
  return res;
};

export const updateSystemExternalLink = async (params) => {
  const res = await put(
    `/system/external_link/${params.id}`,
    params?.values,
  );
  return res;
};

export const systemExternalLinkApis: CommonApis = {
  entity: "SystemExternalLink",
  query: getSystemExternalLinkList,
  create: createSystemExternalLink,
  remove: delSystemExternalLink,
  removes: delSystemExternalLinks,
  update: updateSystemExternalLink,
  get: getSystemExternalLink,
};
