/**
 * 特种作业统计下钻功能相关类型定义
 */

export interface SpecialWorkCategoryStats {
  id: number;
  name: string;
  count: number;
  icon: string;
}

export interface SpecialWorkCategoryInfo {
  total: number;
  jobCategoryStats: SpecialWorkCategoryStats[];
}

export interface SpecialWorkStatsModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  categoryInfo: SpecialWorkCategoryInfo | null;
}

export interface SpecialWorkDrilldownState {
  isOpen: boolean;
  title: string;
  categoryInfo: SpecialWorkCategoryInfo | null;
}

// 为了向后兼容，保留旧的类型别名
export type JobCategoryStats = SpecialWorkCategoryStats;
export type CategoryInfo = SpecialWorkCategoryInfo;
export type DrilldownModalProps = SpecialWorkStatsModalProps;
export type DrilldownState = SpecialWorkDrilldownState;
