/**
 * 特种作业统计下钻功能数据验证工具
 */

import type {
  SpecialWorkCategoryInfo,
  SpecialWorkCategoryStats,
} from "@/types/drilldown";

/**
 * 验证单个作业分类统计数据
 */
function validateJobCategoryStats(
  item: unknown
): item is SpecialWorkCategoryStats {
  if (!item || typeof item !== "object") {
    return false;
  }

  const stats = item as Record<string, unknown>;

  return (
    typeof stats.id === "number" &&
    typeof stats.name === "string" &&
    stats.name.trim().length > 0 &&
    typeof stats.count === "number" &&
    stats.count >= 0 &&
    typeof stats.icon === "string"
  );
}

/**
 * 验证作业分类信息数据结构
 */
export function validateCategoryInfo(
  categoryInfo: unknown
): SpecialWorkCategoryInfo | null {
  if (!categoryInfo || typeof categoryInfo !== "object") {
    return null;
  }

  const info = categoryInfo as Record<string, unknown>;

  // 验证 total 字段
  if (typeof info.total !== "number" || info.total < 0) {
    return null;
  }

  // 验证 jobCategoryStats 字段
  if (!Array.isArray(info.jobCategoryStats)) {
    return null;
  }

  // 过滤并验证每个统计项
  const validStats = info.jobCategoryStats.filter(validateJobCategoryStats);

  return {
    total: info.total,
    jobCategoryStats: validStats,
  };
}

/**
 * 检查数据是否为空
 */
export function isCategoryInfoEmpty(
  categoryInfo: SpecialWorkCategoryInfo | null
): boolean {
  if (!categoryInfo) {
    return true;
  }

  return (
    categoryInfo.total === 0 ||
    !categoryInfo.jobCategoryStats ||
    categoryInfo.jobCategoryStats.length === 0
  );
}

/**
 * 获取数据验证错误信息
 */
export function getCategoryInfoErrorMessage(categoryInfo: unknown): string {
  if (!categoryInfo) {
    return "数据不存在";
  }

  if (typeof categoryInfo !== "object") {
    return "数据格式错误";
  }

  const info = categoryInfo as Record<string, unknown>;

  if (typeof info.total !== "number") {
    return "总数数据格式错误";
  }

  if (info.total < 0) {
    return "总数不能为负数";
  }

  if (!Array.isArray(info.jobCategoryStats)) {
    return "分类统计数据格式错误";
  }

  if (info.jobCategoryStats.length === 0) {
    return "暂无分类统计数据";
  }

  return "数据验证失败";
}

/**
 * 安全地获取分类统计数据
 */
export function getSafeCategoryInfo(categoryInfo: unknown): {
  data: SpecialWorkCategoryInfo | null;
  error: string | null;
  isEmpty: boolean;
} {
  try {
    const validatedData = validateCategoryInfo(categoryInfo);

    if (!validatedData) {
      return {
        data: null,
        error: getCategoryInfoErrorMessage(categoryInfo),
        isEmpty: true,
      };
    }

    const isEmpty = isCategoryInfoEmpty(validatedData);

    return {
      data: validatedData,
      error: null,
      isEmpty,
    };
  } catch (error) {
    return {
      data: null,
      error: "数据处理异常",
      isEmpty: true,
    };
  }
}
