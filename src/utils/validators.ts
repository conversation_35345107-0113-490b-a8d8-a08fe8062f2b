// 公共校验工具：手机号等
// 统一导出供各业务模块复用

// 中国大陆手机号：以 1 开头，第二位 3-9，后续 9 位数字，总计 11 位
export const CN_MOBILE_REGEX = /^1[3-9]\d{9}$/;

/**
 * 判断是否为合法的中国大陆手机号
 */
export function isValidCnMobile(value?: string): boolean {
  if (!value) return false;
  return CN_MOBILE_REGEX.test(String(value).trim());
}

/**
 * Semi UI 自定义校验器，失败时返回 Error，通过返回 true
 * 默认提示文案可在各业务调用处覆盖
 */
export function semiPhoneValidator(_rule: any, value: string) {
  if (!value) return new Error("请输入手机号码");
  if (!isValidCnMobile(value)) return new Error("请输入有效的手机号码");
  return true;
}

// 统一社会信用代码（USCC）校验
// 参考国家标准 GB 32100-2015：18 位，含校验位；字符集不含 I、O、S、V、Z
const USCC_CHARSET = "0123456789ABCDEFGHJKLMNPQRTUWXY";
const USCC_WEIGHTS = [
  1, 3, 9, 27, 19, 26, 16, 17, 20, 29, 25, 13, 8, 24, 10, 30, 28,
];

function calcUsccCheckCode(code17: string): string {
  let sum = 0;
  for (let i = 0; i < 17; i++) {
    const ch = code17[i];
    const val = USCC_CHARSET.indexOf(ch);
    if (val === -1) return "";
    sum += val * USCC_WEIGHTS[i];
  }
  const logicCheck = (31 - (sum % 31)) % 31;
  return USCC_CHARSET[logicCheck];
}

function isValidUscc(input?: string): boolean {
  if (!input) return false;
  const code = input.toUpperCase();
  // 基本格式：18 位，首位[159Y]，第二位[1239]，地区码 6 位数字，组织机构标识 9 位，末位为校验码
  // const basicRegex = /^[159Y][1239]\d{6}[0-9A-Z]{10}$/;
  const basicRegex = /^[159Y][1239]\d{6}[0-9A-HJKNPR-UWXY]{10}$/;
  if (!basicRegex.test(code)) return false;
  const check = calcUsccCheckCode(code.slice(0, 17));
  return check !== "" && check === code[17];
}

export function semiUsccValidator(_rule: any, value: string) {
  if (!value) return new Error("请输入统一社会信用代码");
  if (!isValidUscc(value)) return new Error("统一社会信用代码格式或校验位不正确");
  return true;
}

// 中国居民身份证号校验（18位）：包含出生日期有效性与校验码
const ID_CHECK_WEIGHTS = [
  7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2,
];
const ID_CHECK_CODES = [
  "1",
  "0",
  "X",
  "9",
  "8",
  "7",
  "6",
  "5",
  "4",
  "3",
  "2",
];

export function isValidChineseId(input?: string): boolean {
  if (!input) return false;
  const code = String(input).toUpperCase().trim();
  // 基本格式：18位，前17位数字，最后一位为数字或X
  if (!/^\d{17}[0-9X]$/.test(code)) return false;

  // 出生日期校验：YYYYMMDD（第7-14位）
  const year = Number(code.slice(6, 10));
  const month = Number(code.slice(10, 12));
  const day = Number(code.slice(12, 14));
  const date = new Date(year, month - 1, day);
  if (
    date.getFullYear() !== year ||
    date.getMonth() + 1 !== month ||
    date.getDate() !== day
  ) {
    return false;
  }

  // 校验码计算
  let sum = 0;
  for (let i = 0; i < 17; i++) {
    sum += parseInt(code[i], 10) * ID_CHECK_WEIGHTS[i];
  }
  const mod = sum % 11;
  const checkCode = ID_CHECK_CODES[mod];
  return checkCode === code[17];
}

// Semi UI 校验器
export function semiIdCardValidator(_rule: any, value: string) {
  if (!value) return new Error("请输入身份证号");
  if (!isValidChineseId(value)) return new Error("身份证号格式或校验位不正确");
  return true;
}