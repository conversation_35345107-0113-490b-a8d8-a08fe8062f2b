/**
 * 特种作业统计下钻功能数据验证工具测试
 */

import {
  getCategoryInfoErrorMessage,
  getSafeCategoryInfo,
  isCategoryInfoEmpty,
  validateCategoryInfo,
} from "../drilldownValidation";

describe("drilldownValidation", () => {
  describe("validateCategoryInfo", () => {
    it("应该验证有效的分类信息", () => {
      const validData = {
        total: 100,
        jobCategoryStats: [
          { id: 1, name: "高处作业", count: 50, icon: "height" },
          { id: 2, name: "动火作业", count: 30, icon: "fire" },
        ],
      };

      const result = validateCategoryInfo(validData);
      expect(result).toEqual(validData);
    });

    it("应该过滤无效的统计项", () => {
      const dataWithInvalidItems = {
        total: 100,
        jobCategoryStats: [
          { id: 1, name: "高处作业", count: 50, icon: "height" },
          { id: "invalid", name: "", count: -10, icon: "fire" }, // 无效项
          { id: 2, name: "动火作业", count: 30, icon: "fire" },
        ],
      };

      const result = validateCategoryInfo(dataWithInvalidItems);
      expect(result?.jobCategoryStats).toHaveLength(2);
      expect(result?.jobCategoryStats[0].name).toBe("高处作业");
      expect(result?.jobCategoryStats[1].name).toBe("动火作业");
    });

    it("应该返回 null 对于无效数据", () => {
      expect(validateCategoryInfo(null)).toBeNull();
      expect(validateCategoryInfo(undefined)).toBeNull();
      expect(validateCategoryInfo("invalid")).toBeNull();
      expect(validateCategoryInfo({ total: -1 })).toBeNull();
      expect(
        validateCategoryInfo({ total: 10, jobCategoryStats: "not-array" })
      ).toBeNull();
    });
  });

  describe("isCategoryInfoEmpty", () => {
    it("应该识别空数据", () => {
      expect(isCategoryInfoEmpty(null)).toBe(true);
      expect(isCategoryInfoEmpty({ total: 0, jobCategoryStats: [] })).toBe(
        true
      );
      expect(isCategoryInfoEmpty({ total: 10, jobCategoryStats: [] })).toBe(
        true
      );
    });

    it("应该识别非空数据", () => {
      const nonEmptyData = {
        total: 10,
        jobCategoryStats: [
          { id: 1, name: "高处作业", count: 10, icon: "height" },
        ],
      };
      expect(isCategoryInfoEmpty(nonEmptyData)).toBe(false);
    });
  });

  describe("getCategoryInfoErrorMessage", () => {
    it("应该返回正确的错误信息", () => {
      expect(getCategoryInfoErrorMessage(null)).toBe("数据不存在");
      expect(getCategoryInfoErrorMessage("invalid")).toBe("数据格式错误");
      expect(getCategoryInfoErrorMessage({ total: "invalid" })).toBe(
        "总数数据格式错误"
      );
      expect(getCategoryInfoErrorMessage({ total: -1 })).toBe("总数不能为负数");
      expect(
        getCategoryInfoErrorMessage({ total: 10, jobCategoryStats: "invalid" })
      ).toBe("分类统计数据格式错误");
      expect(
        getCategoryInfoErrorMessage({ total: 10, jobCategoryStats: [] })
      ).toBe("暂无分类统计数据");
    });
  });

  describe("getSafeCategoryInfo", () => {
    it("应该返回安全的数据处理结果", () => {
      const validData = {
        total: 100,
        jobCategoryStats: [
          { id: 1, name: "高处作业", count: 50, icon: "height" },
        ],
      };

      const result = getSafeCategoryInfo(validData);
      expect(result.data).toEqual(validData);
      expect(result.error).toBeNull();
      expect(result.isEmpty).toBe(false);
    });

    it("应该处理无效数据", () => {
      const result = getSafeCategoryInfo(null);
      expect(result.data).toBeNull();
      expect(result.error).toBe("数据不存在");
      expect(result.isEmpty).toBe(true);
    });

    it("应该处理异常情况", () => {
      // 模拟会抛出异常的数据
      const problematicData = {
        get total() {
          throw new Error("Access error");
        },
      };

      const result = getSafeCategoryInfo(problematicData);
      expect(result.data).toBeNull();
      expect(result.error).toBe("数据处理异常");
      expect(result.isEmpty).toBe(true);
    });
  });
});
