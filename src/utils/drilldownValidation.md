# 特种作业统计下钻功能数据验证和错误处理

## 概述

本模块提供了特种作业统计下钻功能的数据验证和错误处理工具，确保数据的完整性和用户体验的一致性。

## 核心功能

### 1. 数据验证 (`validateCategoryInfo`)

验证作业分类信息数据结构的完整性和正确性：

```typescript
const categoryInfo = {
  total: 100,
  jobCategoryStats: [
    { id: 1, name: "高处作业", count: 50, icon: "height" },
    { id: 2, name: "动火作业", count: 30, icon: "fire" },
  ],
};

const validatedData = validateCategoryInfo(categoryInfo);
// 返回验证后的数据或 null
```

**验证规则：**

- `total` 必须是非负数字
- `jobCategoryStats` 必须是数组
- 每个统计项必须包含：
  - `id`: 数字类型
  - `name`: 非空字符串
  - `count`: 非负数字
  - `icon`: 字符串类型

### 2. 空数据检查 (`isCategoryInfoEmpty`)

检查数据是否为空或无效：

```typescript
const isEmpty = isCategoryInfoEmpty(categoryInfo);
// 返回 boolean 值
```

**空数据条件：**

- 数据为 null 或 undefined
- total 为 0
- jobCategoryStats 为空数组

### 3. 错误信息生成 (`getCategoryInfoErrorMessage`)

根据数据问题生成用户友好的错误信息：

```typescript
const errorMessage = getCategoryInfoErrorMessage(invalidData);
// 返回具体的错误描述
```

**错误类型：**

- 数据不存在
- 数据格式错误
- 总数数据格式错误
- 总数不能为负数
- 分类统计数据格式错误
- 暂无分类统计数据

### 4. 安全数据处理 (`getSafeCategoryInfo`)

综合处理数据验证、错误处理和空数据检查：

```typescript
const { data, error, isEmpty } = getSafeCategoryInfo(categoryInfo);

if (error) {
  // 处理错误
  console.error(error);
} else if (isEmpty) {
  // 显示空状态
  showEmptyState();
} else {
  // 使用验证后的数据
  renderChart(data);
}
```

## 错误处理策略

### 1. 分层错误处理

- **数据层**: 验证和过滤无效数据
- **组件层**: 处理用户交互和状态管理
- **UI层**: 展示错误信息和提供恢复选项

### 2. 用户体验优化

- **加载状态**: 显示数据处理进度
- **错误反馈**: 提供清晰的错误信息
- **恢复机制**: 提供重试和关闭选项
- **数据质量提示**: 告知用户数据过滤情况

### 3. 容错处理

- **数据过滤**: 自动过滤无效的统计项
- **异常捕获**: 处理数据访问异常
- **默认值**: 提供合理的默认值

## 使用示例

### 在组件中使用

```typescript
import { getSafeCategoryInfo } from '@/utils/drilldownValidation'
import { useErrorHandler } from '@/hooks/useErrorHandler'
import { useLoadingState } from '@/hooks/useLoadingState'

const MyComponent = ({ categoryInfo }) => {
  const { errorState, handleError, clearError } = useErrorHandler()
  const { isLoading, startLoading, stopLoading } = useLoadingState()

  // 数据验证和处理
  const { data, error, isEmpty } = getSafeCategoryInfo(categoryInfo)

  useEffect(() => {
    if (error) {
      handleError(error, 'validation')
    }
  }, [error, handleError])

  if (errorState.hasError) {
    return <ErrorDisplay error={errorState.errorMessage} onRetry={clearError} />
  }

  if (isEmpty) {
    return <EmptyState />
  }

  return <DataVisualization data={data} />
}
```

### 错误边界集成

```typescript
import { ErrorBoundary } from '@/components/error/ErrorBoundary'

const App = () => (
  <ErrorBoundary
    onError={(error, errorInfo) => {
      console.error('Application Error:', error, errorInfo)
    }}
  >
    <MyComponent />
  </ErrorBoundary>
)
```

## 测试覆盖

所有验证函数都有完整的单元测试覆盖：

- ✅ 有效数据验证
- ✅ 无效数据过滤
- ✅ 空数据检查
- ✅ 错误信息生成
- ✅ 异常情况处理

运行测试：

```bash
npm test -- --run src/utils/__tests__/drilldownValidation.test.ts
```

## 最佳实践

1. **始终使用 `getSafeCategoryInfo`** 进行数据处理
2. **结合错误处理 Hook** 管理错误状态
3. **提供用户友好的错误信息** 和恢复选项
4. **使用错误边界** 捕获组件级别的错误
5. **添加加载状态** 提升用户体验
6. **记录错误信息** 便于调试和监控

## 相关文件

- `src/utils/drilldownValidation.ts` - 核心验证逻辑
- `src/hooks/useErrorHandler.ts` - 错误处理 Hook
- `src/hooks/useLoadingState.ts` - 加载状态 Hook
- `src/components/error/ErrorBoundary.tsx` - 错误边界组件
- `src/components/ui/EmptyState.tsx` - 空状态组件
- `src/utils/__tests__/drilldownValidation.test.ts` - 单元测试
